using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.VideoEntity
{
    /// <summary>
    /// 批次表
    /// </summary>
    [Table("batches")]
    public class Batch : BaseEntity_ID
    {
        /// <summary>
        /// 批次名称
        /// </summary>
        [Required]
        [MaxLength(100)]
        [Comment("批次名称")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 批次描述
        /// </summary>
        [MaxLength(255)]
        [Comment("批次描述")]
        public string? Description { get; set; }

        /// <summary>
        /// 关联视频ID
        /// </summary>
        [Comment("关联视频ID")]
        public int VideoId { get; set; }

        /// <summary>
        /// 视频标题(冗余)
        /// </summary>
        [Required]
        [MaxLength(255)]
        [Comment("视频标题(冗余)")]
        public string VideoTitle { get; set; } = string.Empty;

        /// <summary>
        /// 视频描述(冗余)
        /// </summary>
        [Comment("视频描述(冗余)")]
        public string? VideoDescription { get; set; }

        /// <summary>
        /// 封面URL(冗余)
        /// </summary>
        [MaxLength(255)]
        [Comment("封面URL(冗余)")]
        public string? VideoCoverUrl { get; set; }

        /// <summary>
        /// 视频URL(冗余)
        /// </summary>
        [Required]
        [MaxLength(255)]
        [Comment("视频URL(冗余)")]
        public string VideoUrl { get; set; } = string.Empty;

        /// <summary>
        /// 视频时长(冗余)
        /// </summary>
        [Comment("视频时长(冗余)")]
        public int VideoDuration { get; set; } = 0;

        /// <summary>
        /// 当前参与人数
        /// </summary>
        [Comment("当前参与人数")]
        public int CurrentParticipants { get; set; } = 0;

        /// <summary>
        /// 红包金额(冗余)
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        [Comment("红包金额(冗余)")]
        public decimal RewardAmount { get; set; } = 0.00m;

        /// <summary>
        /// 红包金额
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        [Comment("红包金额")]
        public decimal RedPacketAmount { get; set; } = 0.00m;

        /// <summary>
        /// 问题JSON(冗余) - 格式: [{"questionText":"问题内容","orderNum":0,"options":[{"optionText":"选项1","isCorrect":true,"orderNum":0},{"optionText":"选项2","isCorrect":false,"orderNum":1}]}]
        /// </summary>
        [Comment("问题JSON(冗余) - 格式: [{'questionText':'问题内容','orderNum':0,'options':[{'optionText':'选项1','isCorrect':true,'orderNum':0},{'optionText':'选项2','isCorrect':false,'orderNum':1}]}]")]
        public string? Questions { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        [Required]
        [Comment("开始时间")]
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [Required]
        [Comment("结束时间")]
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        [Comment("创建人ID")]
        public string CreatorId { get; set; } = string.Empty;

        /// <summary>
        /// 状态:0下线,1上线
        /// </summary>
        [Comment("状态:0下线,1上线")]
        public byte Status { get; set; } = 1;
    }
}
