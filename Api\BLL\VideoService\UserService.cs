using BLL.Common;
using BLL.SysService;
using Common.Autofac;
using Common.Exceptions;
using Common.JWT;
using DAL.SysDAL;
using DAL.VideoDAL;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Entity.Entitys.VideoEntity;
using Microsoft.Extensions.Logging;

namespace BLL.VideoService
{
    /// <summary>
    /// 用户业务服务类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class UserService(
        UserDAL userDAL,
        UserAuditDAL userAuditDAL,
        SysUserDAL sysUserDAL,
        SysLogService logService,
        UserScopeService userScopeService,
        UserAuditService userAuditService,
        BatchDAL batchDAL) : BasePermissionService(userDAL, sysUserDAL)
    {
        private readonly UserAuditDAL _userAuditDAL = userAuditDAL;
        private readonly SysLogService _logService = logService;
        private readonly UserScopeService _userScopeService = userScopeService;
        private readonly UserAuditService _userAuditService = userAuditService;
        private readonly BatchDAL _batchDAL = batchDAL;

        /// <summary>
        /// 创建用户
        /// </summary>
        /// <param name="createDto">创建用户DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>用户ID</returns>
        public async Task<string> CreateUserAsync(UserCreateDto createDto, CurrentUserInfoDto currentUserInfo)
        {
            // 检查OpenID是否存在
            if (!string.IsNullOrEmpty(createDto.OpenId) && await _userDAL.ExistsOpenIdAsync(createDto.OpenId))
                throw new BusinessException($"OpenID {createDto.OpenId} 已存在");

            // 创建用户实体
            var user = new User
            {
                OpenId = createDto.OpenId,
                UnionId = createDto.UnionId,
                Nickname = createDto.Nickname,
                Avatar = createDto.Avatar,
                EmployeeId = createDto.EmployeeId,
                CreateTime = DateTime.Now,
                CreatedBy = currentUserInfo.UserId,
                CreatorName = currentUserInfo.UserName
            };

            // 添加用户
            await _userDAL.AddAsync(user);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户管理",
                Operation = "创建用户",
                BusinessObject = "User",
                ObjectId = user.Id.ToString(),
                DetailedInfo = $"创建用户：{user.Nickname}，绑定员工：{user.EmployeeId}",
                AfterData = user,
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return user.Id;
        }





        /// <summary>
        /// 根据OpenID获取用户
        /// </summary>
        /// <param name="openId">微信OpenID</param>
        /// <returns>用户响应DTO</returns>
        public async Task<UserResponseDto?> GetUserByOpenIdAsync(string openId)
        {
            var user = await _userDAL.GetByOpenIdAsync(openId);
            if (user == null) return null;

            return new UserResponseDto
            {
                Id = user.Id,
                OpenId = user.OpenId,
                UnionId = user.UnionId,
                Nickname = user.Nickname,
                Avatar = user.Avatar,
                EmployeeId = user.EmployeeId,
                LastLogin = user.LastLogin,
                CreateTime = user.CreateTime
            };
        }

        /// <summary>
        /// 获取用户详情
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户响应DTO</returns>
        public async Task<UserResponseDto?> GetUserAsync(string userId)
        {
            var user = await _userDAL.GetByIdAsync(userId);
            if (user == null) return null;

            return new UserResponseDto
            {
                Id = user.Id,
                OpenId = user.OpenId,
                UnionId = user.UnionId,
                Nickname = user.Nickname,
                Avatar = user.Avatar,
                EmployeeId = user.EmployeeId,
                LastLogin = user.LastLogin,
                CreateTime = user.CreateTime,
            };
        }

        /// <summary>
        /// 获取待审核用户列表
        /// </summary>
        /// <returns>待审核用户列表</returns>
        public async Task<List<UserResponseDto>> GetPendingAuditUsersAsync()
        {
            var audits = await _userAuditDAL.GetPendingAuditUsersAsync();
            var responseList = new List<UserResponseDto>();

            foreach (var audit in audits)
            {
                var user = await _userDAL.GetByIdAsync(audit.UserId);
                if (user != null)
                {
                    responseList.Add(new UserResponseDto
                    {
                        Id = user.Id,
                        OpenId = user.OpenId,
                        UnionId = user.UnionId,
                        Nickname = user.Nickname,
                        Avatar = user.Avatar,
                        EmployeeId = user.EmployeeId,
                        CreateTime = user.CreateTime,
                        LastLogin = user.LastLogin
                    });
                }
            }

            return responseList;
        }

        /// <summary>
        /// 获取指定员工的待审核用户列表
        /// </summary>
        /// <param name="employeeId">员工ID</param>
        /// <returns>待审核用户列表</returns>
        public async Task<List<UserResponseDto>> GetPendingAuditUsersByEmployeeAsync(string employeeId)
        {
            // 获取属于该员工且状态为待审核的用户
            var users = await _userDAL.GetByEmployeeIdAsync(employeeId);
            var responseList = new List<UserResponseDto>();

            foreach (var user in users)
            {
                // 检查用户是否有待审核记录或者没有审核记录（新用户）
                var latestAudit = await _userAuditDAL.GetLatestUserAuditAsync(user.Id);

                // 如果没有审核记录或者最新审核记录状态为待审核(0)，则加入列表
                if (latestAudit == null || latestAudit.Status == 0)
                {
                    responseList.Add(new UserResponseDto
                    {
                        Id = user.Id,
                        OpenId = user.OpenId,
                        UnionId = user.UnionId,
                        Nickname = user.Nickname,
                        Avatar = user.Avatar,
                        EmployeeId = user.EmployeeId,
                        CreateTime = user.CreateTime,
                        LastLogin = user.LastLogin
                    });
                }
            }

            return responseList;
        }

        /// <summary>
        /// 分页查询用户列表（带权限过滤）
        /// </summary>
        /// <param name="queryDto">查询条件DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<UserResponseDto>> GetUserPagedListAsync(UserQueryDto queryDto, UserInfo? currentUserInfo = null)
        {
            var queryable = new UserDAL.Queryable
            {
                Nickname = queryDto.Nickname,
                EmployeeId = queryDto.EmployeeId,
                StartTime = queryDto.StartTime,
                EndTime = queryDto.EndTime,
                PageIndex = queryDto.PageIndex,
                PageSize = queryDto.PageSize
            };

            // 根据用户权限过滤数据
            if (currentUserInfo != null)
            {
                // 验证权限
                ValidateUserTypePermission(currentUserInfo, [1, 2, 3], "权限不足，无法查看用户列表");

                // 根据用户类型应用权限过滤
                var accessibleUserIds = await GetAccessibleUserIdsAsync(currentUserInfo);
                if (accessibleUserIds != null)
                {
                    // 管理员或员工：只能查看权限范围内的用户
                    queryable.UserIds = accessibleUserIds;
                }
                // 超级管理员：accessibleUserIds为null，可以查看所有用户
            }

            var pageEntity = await _userDAL.GetPagedListAsync(queryable);

            var items = (pageEntity.List ?? []).Select(user => new UserResponseDto
            {
                Id = user.Id,
                OpenId = user.OpenId,
                UnionId = user.UnionId,
                Nickname = user.Nickname,
                Avatar = user.Avatar,
                EmployeeId = user.EmployeeId,
                CreateTime = user.CreateTime,
                LastLogin = user.LastLogin
            }).ToList();

            return new PagedResult<UserResponseDto>
            {
                Items = items,
                TotalCount = pageEntity.TotalCount,
                PageIndex = pageEntity.PageIndex,
                PageSize = pageEntity.PageSize
            };
        }

        /// <summary>
        /// 微信登录
        /// </summary>
        /// <param name="wechatLoginDto">微信登录DTO</param>
        /// <returns>登录响应</returns>
        public async Task<Entity.Dto.VideoDto.LoginResponseDto> WechatLoginAsync(WechatLoginDto wechatLoginDto)
        {
            // TODO: 实现微信授权码换取用户信息的逻辑
            // 这里需要调用微信API获取用户信息

            // 模拟从微信获取的用户信息
            var openId = "mock_openid_" + wechatLoginDto.Code;

            // 查找或创建用户
            var user = await _userDAL.GetByOpenIdAsync(openId);
            _ = user == null;

            if (user == null)
            {
                // 创建新用户
                user = new User
                {
                    OpenId = openId,
                    Nickname = "微信用户",
                    EmployeeId = wechatLoginDto.EmployeeId, // 绑定员工ID
                    CreateTime = DateTime.Now
                };
                await _userDAL.AddAsync(user);

                // 记录用户注册日志
                await _logService.LogBusinessOperationAsync(new BusinessLogDto
                {
                    Module = "用户管理",
                    Operation = "微信用户注册",
                    BusinessObject = "User",
                    ObjectId = user.Id.ToString(),
                    DetailedInfo = $"微信用户注册，OpenId：{openId}，绑定员工：{wechatLoginDto.EmployeeId}",
                    AfterData = user,
                    Level = LogLevel.Information
                });

                // 所有新用户都需要经过审核流程
                if (!string.IsNullOrEmpty(wechatLoginDto.EmployeeId))
                {
                    try
                    {
                        await _userAuditService.CreateUserAuditWithAutoApprovalAsync(
                            user.Id,
                            wechatLoginDto.EmployeeId,
                            wechatLoginDto.BatchId,
                            null);
                    }
                    catch (BusinessException ex)
                    {
                        // 如果审核记录创建失败，记录日志但不影响登录
                        await _logService.LogBusinessOperationAsync(new BusinessLogDto
                        {
                            Module = "用户审核",
                            Operation = "创建审核记录失败",
                            BusinessObject = "UserAudit",
                            ObjectId = user.Id.ToString(),
                            DetailedInfo = $"用户 {user.Id} 创建审核记录失败：{ex.Message}",
                            Level = LogLevel.Warning
                        });
                    }
                }
            }

            // 更新最后登录时间
            await _userDAL.UpdateLastLoginAsync(user.Id);

            // 生成Token (这里需要实现JWT Token生成逻辑)
            var token = GenerateJwtToken(user);

            return new Entity.Dto.VideoDto.LoginResponseDto
            {
                Token = token,
                UserInfo = new UserResponseDto
                {
                    Id = user.Id,
                    OpenId = user.OpenId,
                    UnionId = user.UnionId,
                    Nickname = user.Nickname,
                    Avatar = user.Avatar,
                    EmployeeId = user.EmployeeId,
                    LastLogin = user.LastLogin,
                    CreateTime = user.CreateTime
                }
            };
        }
        /// <summary>
        /// 处理推广链接访问
        /// </summary>
        /// <param name="accessDto">访问推广链接DTO</param>
        /// <returns>访问结果</returns>
        public async Task<AccessPromotionResponseDto> AccessPromotionLinkAsync(AccessPromotionDto accessDto)
        {
            // 验证批次是否存在
            var batch = await _batchDAL.GetByIdAsync(accessDto.BatchId) ?? throw new BusinessException("指定的批次不存在");

            // 验证员工是否存在（如果提供了员工ID）
            if (!string.IsNullOrEmpty(accessDto.EmployeeId))
            {
                _ = await _sysUserDAL.GetFirstAsync(new DAL.SysDAL.SysUserDAL.UserDALQuery
                {
                    UserId = accessDto.EmployeeId
                }) ?? throw new BusinessException("指定的员工不存在");
            }

            // 如果提供了微信授权码，进行微信登录
            User? user = null;
            if (!string.IsNullOrEmpty(accessDto.Code))
            {
                var wechatLoginDto = new WechatLoginDto
                {
                    Code = accessDto.Code,
                    EmployeeId = accessDto.EmployeeId?.ToString(),
                    BatchId = accessDto.BatchId
                };

                var loginResult = await WechatLoginAsync(wechatLoginDto);
                if (loginResult.UserInfo != null)
                {
                    user = await _userDAL.GetByIdAsync(loginResult.UserInfo.Id);
                }
            }

            // 检查用户审核状态
            byte auditStatus = 1; // 默认通过
            bool needAudit = false;

            if (user != null)
            {
                var userAuditStatus = await _userAuditService.GetUserAuditStatusAsync(user.Id);
                if (userAuditStatus.HasValue)
                {
                    auditStatus = userAuditStatus.Value;
                    needAudit = auditStatus == 0; // 待审核状态
                }
            }

            return new AccessPromotionResponseDto
            {
                NeedAudit = needAudit,
                AuditStatus = auditStatus.ToString(),
                Batch = new BatchResponseDto
                {
                    Id = batch.Id,
                    VideoId = batch.VideoId,
                    Name = batch.Name,
                    Description = batch.Description,
                    StartTime = batch.StartTime,
                    EndTime = batch.EndTime,
                    Status = batch.Status,
                    CreateTime = batch.CreateTime
                },
                User = user != null ? new UserResponseDto
                {
                    Id = user.Id,
                    OpenId = user.OpenId,
                    UnionId = user.UnionId,
                    Nickname = user.Nickname,
                    Avatar = user.Avatar,
                    EmployeeId = user.EmployeeId,
                    LastLogin = user.LastLogin,
                    CreateTime = user.CreateTime
                } : null
            };
        }









        /// <summary>
        /// 批量转移用户到新员工（带权限验证）
        /// </summary>
        /// <param name="transferDto">批量转移DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> BatchTransferUsersAsync(UserTransferDto transferDto, CurrentUserInfoDto currentUserInfo)
        {
            // 权限验证：转换CurrentUserInfoDto为UserInfo进行验证
            var userInfo = new UserInfo
            {
                UserId = currentUserInfo.UserId,
                UserName = currentUserInfo.UserName,
                UserType = currentUserInfo.UserType
            };
            ValidateUserTypePermission(userInfo, [1, 2], "权限不足，只有管理员及以上级别可以转移用户");

            // 验证用户是否在权限范围内
            var accessibleUserIds = await GetAccessibleUserIdsAsync(userInfo);
            if (accessibleUserIds != null)
            {
                // 管理员：检查要转移的用户是否在权限范围内
                var unauthorizedUsers = transferDto.UserIds.Where(id => !accessibleUserIds.Contains(id)).ToList();
                if (unauthorizedUsers.Count > 0)
                {
                    throw new BusinessException($"权限不足，无法转移用户：{string.Join(", ", unauthorizedUsers)}");
                }
            }

            // 执行批量转移
            var result = await _userDAL.BatchTransferAsync(
                transferDto.UserIds,
                transferDto.ToEmployeeId);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户管理",
                Operation = "批量转移用户",
                BusinessObject = "User",
                ObjectId = string.Join(",", transferDto.UserIds),
                DetailedInfo = $"批量转移用户至员工 {transferDto.ToEmployeeId}，原因：{transferDto.Reason}",
                BeforeData = new { },
                AfterData = new { transferDto.ToEmployeeId, transferDto.UserIds, transferDto.Reason },
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return result;
        }



        /// <summary>
        /// 获取用户统计信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户统计信息</returns>
        public async Task<UserResponseDto> GetUserStatisticsAsync(string userId)
        {
            var user = await _userDAL.GetByIdAsync(userId)
                ?? throw new BusinessException("用户不存在");

            // TODO: 这里可以添加更多统计信息，如观看次数、红包领取次数等
            // 目前返回基本用户信息
            return new UserResponseDto
            {
                Id = user.Id,
                OpenId = user.OpenId,
                UnionId = user.UnionId,
                Nickname = user.Nickname,
                Avatar = user.Avatar,
                EmployeeId = user.EmployeeId,
                CreateTime = user.CreateTime,
                LastLogin = user.LastLogin
            };
        }

        #region 用户范围查询方法

        /// <summary>
        /// 根据用户范围分页查询用户列表
        /// </summary>
        /// <param name="queryDto">查询条件DTO</param>
        /// <param name="accessibleUserIds">可访问的用户ID列表</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<UserResponseDto>> GetUserPagedListWithScopeAsync(UserQueryDto queryDto, List<string> accessibleUserIds)
        {
            // 如果没有可访问的用户，直接返回空结果
            if (accessibleUserIds.Count == 0)
            {
                return new PagedResult<UserResponseDto>
                {
                    Items = [],
                    TotalCount = 0,
                    PageIndex = queryDto.PageIndex,
                    PageSize = queryDto.PageSize
                };
            }

            var queryable = new UserDAL.Queryable
            {
                Nickname = queryDto.Nickname,
                EmployeeId = queryDto.EmployeeId,
                StartTime = queryDto.StartTime,
                EndTime = queryDto.EndTime,
                PageIndex = queryDto.PageIndex,
                PageSize = queryDto.PageSize
            };

            // 调用DAL层的方法，传入可访问的用户ID列表
            var pageEntity = await _userDAL.GetPagedListWithScopeAsync(queryable, accessibleUserIds);

            var items = (pageEntity.List ?? []).Select(user => new UserResponseDto
            {
                Id = user.Id,
                OpenId = user.OpenId,
                UnionId = user.UnionId,
                Nickname = user.Nickname,
                Avatar = user.Avatar,
                EmployeeId = user.EmployeeId,
                CreateTime = user.CreateTime,
                LastLogin = user.LastLogin
            }).ToList();

            return new PagedResult<UserResponseDto>
            {
                Items = items,
                TotalCount = pageEntity.TotalCount,
                PageIndex = pageEntity.PageIndex,
                PageSize = pageEntity.PageSize
            };
        }

        /// <summary>
        /// 根据当前用户权限分页查询用户列表
        /// </summary>
        /// <param name="queryDto">查询条件DTO</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<UserResponseDto>> GetUserPagedListByCurrentUserAsync(UserQueryDto queryDto, string currentUserId)
        {
            // 获取当前用户可访问的用户ID列表
            var accessibleUserIds = await _userScopeService.GetAccessibleUserIdsAsync(currentUserId);

            // 调用上面的方法进行查询
            return await GetUserPagedListWithScopeAsync(queryDto, accessibleUserIds);
        }

        /// <summary>
        /// 根据员工ID列表获取员工信息
        /// </summary>
        /// <param name="employeeIds">员工ID列表</param>
        /// <returns>员工信息列表</returns>
        public async Task<List<SysUserDto>> GetEmployeesByIdsAsync(List<string> employeeIds)
        {
            if (employeeIds == null || employeeIds.Count == 0)
                return [];

            // 调用SysUserDAL获取员工信息
            var employees = new List<SysUserDto>();

            foreach (var employeeId in employeeIds)
            {
                var employee = await _sysUserDAL.GetByIdAsync(employeeId);
                if (employee != null && employee.UserType == 3) // 确保是员工类型
                {
                    employees.Add(new SysUserDto
                    {
                        Id = employee.UserId,
                        UserName = employee.UserName,
                        RealName = employee.RealName,
                        Mobile = employee.Mobile,
                        Email = employee.Email,
                        Avatar = employee.Avatar,
                        Status = employee.Status,
                        LastLoginTime = employee.LastLoginTime,
                        CreateTime = employee.CreateTime
                    });
                }
            }

            return employees;
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 生成JWT Token
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <returns>JWT Token</returns>
        private static string GenerateJwtToken(User user)
        {
            // 创建用户信息对象
            var userInfo = new UserInfo
            {
                UserId = user.Id.ToString(),
                UserName = user.Nickname ?? $"用户{user.Id}",
                IsAdmin = false, // 普通用户不是管理员
                UserType = 4, // 普通用户类型，区别于系统用户的1-3
                Roles = [] // 普通用户没有特殊角色
            };

            // 使用JWT帮助类生成真实的JWT令牌
            return JWTHelper.CreateJwt(userInfo);
        }

        #endregion
    }
}
