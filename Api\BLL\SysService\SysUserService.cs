using BLL.VideoService;
using Common;
using Common.Autofac;
using Common.Exceptions;
using Common.Helper;
using Common.JWT;
using DAL.SysDAL;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Entity.Entitys.SysEntity;
using Entity.Extensions;
using static DAL.SysDAL.SysUserDAL;

namespace BLL.SysService
{
    /// <summary>
    /// 用户服务实现类,处理用户相关的业务逻辑
    /// </summary>
    /// <remarks>
    /// 构造函数,通过依赖注入获取用户数据访问层实例
    /// </remarks>
    /// <param name="userDLL">用户数据访问层接口</param>
    [Dependency(DependencyType.Scoped)]
    public class SysUserService(SysUserDAL userDLL)
    {
        /// <summary>
        /// 用户数据访问层接口
        /// </summary>
        private readonly SysUserDAL _userDLL = userDLL;

        /// <summary>
        /// Redis中存储用户登录状态的key前缀
        /// </summary>
        private const string USER_LOGIN_STATUS_PREFIX = "UserLoginStatus:";

        /// <summary>
        /// 用户登录状态过期时间（与JWT token过期时间保持一致）
        /// </summary>
        private static readonly TimeSpan LOGIN_STATUS_EXPIRY = TimeSpan.FromSeconds(JWTSetting.Exp);



        /// <summary>
        /// 验证用户创建权限
        /// 只有超管能创建管理员，管理员只能创建员工
        /// </summary>
        /// <param name="targetUserType">要创建的用户类型</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <exception cref="BusinessException">权限不足时抛出异常</exception>
        private async Task ValidateUserCreationPermissionAsync(byte targetUserType, string currentUserId)
        {
            // 获取当前用户信息
            var currentUser = await _userDLL.GetFirstAsync(new UserDALQuery { UserId = currentUserId })
                ?? throw new BusinessException("当前用户不存在");

            // 验证权限规则
            switch (targetUserType)
            {
                case 1: // 超级管理员
                    throw new BusinessException("不允许创建超级管理员账户");

                case 2: // 管理员
                    if (currentUser.UserType != 1)
                        throw new BusinessException("只有超级管理员才能创建管理员账户");
                    break;

                case 3: // 员工
                    if (currentUser.UserType != 1 && currentUser.UserType != 2)
                        throw new BusinessException("只有超级管理员和管理员才能创建员工账户");
                    break;

                default:
                    throw new BusinessException("无效的用户类型");
            }
        }

        /// <summary>
        /// 获取用户的Redis key
        /// </summary>
        private static string GetUserKey(string userId) => $"{USER_LOGIN_STATUS_PREFIX}{userId}";




        /// <summary>
        /// 创建新用户
        /// </summary>
        /// <param name="input">用户创建信息DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>新创建用户的ID</returns>
        /// <exception cref="BusinessException">当用户名已存在或权限不足时抛出异常</exception>
        public async Task<string> CreateAsync(SysCreateUserDto input, CurrentUserInfoDto currentUserInfo)
        {
            // 验证用户创建权限
            await ValidateUserCreationPermissionAsync(input.UserType, currentUserInfo.UserId);

            // 一次性验证用户名、邮箱和手机号的唯一性
            var duplicates = await _userDLL.CheckUserInfoUniqueAsync(
                input.UserName, input.Email, input.Mobile);

            // 如果有重复字段，抛出异常
            if (duplicates.Count > 0)
            {
                var firstDuplicate = duplicates.First();
                string fieldName = firstDuplicate.Key switch
                {
                    "UserName" => "用户名",
                    "Email" => "邮箱",
                    "Mobile" => "手机号",
                    _ => firstDuplicate.Key
                };
                throw new BusinessException($"{fieldName} {firstDuplicate.Value} 已被使用");
            }

            // 创建新用户实体并设置属性
            var user = new SysUser
            {
                UserId = Guid.NewGuid().ToString("N"),
                UserName = input.UserName,
                Password = EncryptHelper.EncryptPassword(input.Password), // 加密密码
                RealName = input.RealName,
                Avatar = input.Avatar,
                Email = input.Email,
                Mobile = input.Mobile,
                Status = 1,
                UserType = input.UserType
            };
            user.InitializeForAdd(currentUserInfo);
            await _userDLL.AddAsync(user);
            return user.UserId;
        }



        /// <summary>
        /// 更新用户信息
        /// </summary>
        /// <param name="input">用户更新信息DTO</param>
        /// <exception cref="BusinessException">当用户不存在时抛出异常</exception>
        public async Task UpdateAsync(SysUpdateUserDto input, CurrentUserInfoDto currentUserInfo)
        {
            // 获取并验证用户是否存在
            var user = await _userDLL.GetFirstAsync(new UserDALQuery { UserId = input.UserId })
                ?? throw new BusinessException($"用户 {input.UserId} 不存在");

            // 一次性验证邮箱和手机号的唯一性（更新时不需要验证用户名）
            var duplicates = await _userDLL.CheckUserInfoUniqueAsync(
                null, input.Email, input.Mobile, input.UserId);

            // 如果有重复字段，抛出异常
            if (duplicates.Count > 0)
            {
                var firstDuplicate = duplicates.First();
                string fieldName = firstDuplicate.Key switch
                {
                    "Email" => "邮箱",
                    "Mobile" => "手机号",
                    _ => firstDuplicate.Key
                };
                throw new BusinessException($"{fieldName} {firstDuplicate.Value} 已被其他用户使用");
            }

            // 更新用户信息
            user.RealName = input.RealName;
            user.Avatar = input.Avatar;
            user.Email = input.Email;
            user.Mobile = input.Mobile;
            user.Status = input.Status;
            user.InitializeForUpdate(currentUserInfo);
            await _userDLL.UpdateAsync(user);
        }

        /// <summary>
        /// 删除指定用户
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <exception cref="BusinessException">当用户不存在时抛出异常</exception>
        public async Task DeleteAsync(string userId)
        {
            // 获取并验证用户是否存在
            var user = await _userDLL.GetFirstAsync(new UserDALQuery { UserId = userId })
                ?? throw new BusinessException($"用户 {userId} 不存在");

            await _userDLL.DeleteAsync(user);
        }

        /// <summary>
        /// 获取用户详细信息
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>用户详细信息DTO</returns>
        /// <exception cref="BusinessException">当用户不存在时抛出异常</exception>
        public async Task<SysUserDto> GetAsync(string userId)
        {
            // 获取并验证用户是否存在
            var user = await _userDLL.GetFirstAsync(new UserDALQuery { UserId = userId })
                ?? throw new BusinessException($"用户 {userId} 不存在");

            // 转换为DTO并返回
            return new SysUserDto
            {
                Id = user.UserId,
                UserName = user.UserName,
                RealName = user.RealName,
                Avatar = user.Avatar,
                Email = user.Email,
                Mobile = user.Mobile,
                Status = user.Status,
                LastLoginTime = user.LastLoginTime,
                LastLoginIp = user.LastLoginIp ?? string.Empty,
                CreateTime = user.CreateTime,
            };
        }



        /// <summary>
        /// 修改用户密码
        /// </summary>
        /// <param name="input">密码修改信息DTO</param>
        /// <exception cref="BusinessException">当用户不存在或旧密码不正确时抛出异常</exception>
        public async Task ChangePasswordAsync(SysChangePasswordDto input)
        {
            // 获取并验证用户是否存在
            var user = await _userDLL.GetFirstAsync(new UserDALQuery { UserId = input.UserId })
                ?? throw new BusinessException($"用户 {input.UserId} 不存在");

            // 验证旧密码是否正确
            if (user.Password != EncryptHelper.EncryptPassword(input.OldPassword))
                throw new BusinessException("旧密码不正确");


            // 更新密码
            user.Password = EncryptHelper.EncryptPassword(input.NewPassword);

            await _userDLL.UpdateAsync(user);
        }

        /// <summary>
        /// 重置用户密码
        /// </summary>
        /// <param name="input">密码重置信息DTO</param>
        /// <exception cref="BusinessException">当用户不存在时抛出异常</exception>
        public async Task ResetPasswordAsync(SysResetPasswordDto input)
        {
            // 获取并验证用户是否存在
            var user = await _userDLL.GetFirstAsync(new UserDALQuery { UserId = input.UserId })
                ?? throw new BusinessException($"用户 {input.UserId} 不存在");

            // 重置密码
            user.Password = EncryptHelper.EncryptPassword(input.NewPassword);

            await _userDLL.UpdateAsync(user);
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="loginRequest">登录请求信息</param>
        /// <returns>登录响应信息</returns>
        public async Task<SysLoginResponseDto> LoginAsync(SysLoginRequestDto loginRequest)
        {
            // 1. 验证用户名和密码
            var user = await _userDLL.GetFirstAsync(new UserDALQuery { UserName = loginRequest.Username })
                ?? throw new BusinessException("用户不存在");

            if (!string.Equals(loginRequest.Password, user.Password))
                throw new BusinessException("密码错误");

            if (user.Status != 1)
                throw new BusinessException("用户已被禁用");

            // 2. 生成登录响应
            var loginResponse = GenerateLoginResponseAsync(user);


            // 4. 更新用户最后登录信息
            await UpdateUserLoginInfoAsync(user);

            return loginResponse;
        }

        /// <summary>
        /// 生成登录响应信息
        /// </summary>
        /// <param name="user">用户</param>
        /// <returns>登录响应信息</returns>
        public static SysLoginResponseDto GenerateLoginResponseAsync(SysUser user)
        {
            var userInfo = new SysUserInfoDto
            {
                UserId = user.UserId,
                Username = user.UserName,
                NickName = user.RealName,
                UserType = user.UserType
            };
            var jwtUserInfo = new UserInfo
            {
                UserId = user.UserId,
                UserName = user.UserName,
                IsAdmin = user.UserType == 1 || user.UserType == 2,
                UserType = user.UserType,
            };

            var accessToken = JWTHelper.CreateJwt(jwtUserInfo);

            return new SysLoginResponseDto
            {
                AccessToken = accessToken,
                UserInfo = userInfo
            };
        }

        /// <summary>
        /// 更新用户最后登录信息
        /// </summary>
        /// <param name="user">用户</param>
        /// <returns>更新后的用户</returns>
        private async Task UpdateUserLoginInfoAsync(SysUser user)
        {
            user.LastLoginTime = DateTime.Now;
            user.LastLoginIp = ""; // 这里可以添加获取IP的逻辑
            await _userDLL.UpdateAsync(user);
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息DTO</returns>
        public async Task<SysUserInfoDto> GetUserInfoAsync(string userId)
        {
            // 查询用户基本信息
            var user = await _userDLL.GetFirstAsync(new UserDALQuery { UserId = userId }) ?? throw new BusinessException("用户不存在");

            // 构建用户信息DTO
            return new SysUserInfoDto
            {
                UserId = userId,
                Username = user.UserName,
                NickName = user.RealName,
                UserType = user.UserType
            };
        }

        #region 层级查询和统计方法

        /// <summary>
        /// 获取管理员列表（带统计数据）
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>管理员列表</returns>
        public async Task<List<SysUserWithStatisticsDto>> GetAdministratorsWithStatisticsAsync(SubordinateQueryDto query, CurrentUserInfoDto currentUserInfo)
        {
            // 权限检查：只有超级管理员可以查看所有管理员
            if (currentUserInfo.UserType != 1)
                throw new BusinessException("权限不足，只有超级管理员可以查看管理员列表");

            var administrators = await _userDLL.GetByUserTypeAsync(2); // 获取管理员
            return await BuildUsersWithStatisticsAsync(administrators, query);
        }

        /// <summary>
        /// 获取员工列表（带统计数据）
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>员工列表</returns>
        public async Task<List<SysUserWithStatisticsDto>> GetEmployeesWithStatisticsAsync(SubordinateQueryDto query, CurrentUserInfoDto currentUserInfo)
        {
            List<SysUser> employees;

            if (currentUserInfo.UserType == 1) // 超级管理员
            {
                employees = await _userDLL.GetByUserTypeAsync(3); // 获取所有员工
            }
            else if (currentUserInfo.UserType == 2) // 管理员
            {
                employees = await _userDLL.GetByParentUserIdAsync(currentUserInfo.UserId); // 获取自己的下级员工
            }
            else
            {
                throw new BusinessException("权限不足，只有管理员及以上级别可以查看员工列表");
            }

            return await BuildUsersWithStatisticsAsync(employees, query);
        }

        /// <summary>
        /// 获取当前用户的下级列表（带统计数据）
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>下级列表</returns>
        public async Task<List<SysUserWithStatisticsDto>> GetSubordinatesWithStatisticsAsync(SubordinateQueryDto query, CurrentUserInfoDto currentUserInfo)
        {
            return await GetSubordinatesByIdWithStatisticsAsync(currentUserInfo.UserId, query, currentUserInfo);
        }

        /// <summary>
        /// 获取指定用户的下级列表（带统计数据）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="query">查询条件</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>下级列表</returns>
        public async Task<List<SysUserWithStatisticsDto>> GetSubordinatesByIdWithStatisticsAsync(string userId, SubordinateQueryDto query, CurrentUserInfoDto currentUserInfo)
        {
            // 权限检查：确保当前用户有权限查看指定用户的下级
            await ValidateSubordinateAccessPermissionAsync(userId, currentUserInfo);

            var subordinates = await _userDLL.GetByParentUserIdAsync(userId);
            return await BuildUsersWithStatisticsAsync(subordinates, query);
        }

        /// <summary>
        /// 构建带统计数据的用户列表
        /// </summary>
        /// <param name="users">用户列表</param>
        /// <param name="query">查询条件</param>
        /// <returns>带统计数据的用户列表</returns>
        private async Task<List<SysUserWithStatisticsDto>> BuildUsersWithStatisticsAsync(List<SysUser> users, SubordinateQueryDto query)
        {
            var result = new List<SysUserWithStatisticsDto>();

            foreach (var user in users)
            {
                // 应用过滤条件
                if (!string.IsNullOrEmpty(query.UserName) && !user.UserName.Contains(query.UserName))
                    continue;
                if (!string.IsNullOrEmpty(query.RealName) && !user.RealName.Contains(query.RealName))
                    continue;
                if (query.Status.HasValue && user.Status != query.Status.Value)
                    continue;

                var userWithStats = await BuildUserWithStatisticsAsync(user, query);
                result.Add(userWithStats);
            }

            // 分页处理
            var skip = (query.PageIndex - 1) * query.PageSize;
            return [.. result.Skip(skip).Take(query.PageSize)];
        }

        /// <summary>
        /// 构建单个用户的统计数据
        /// </summary>
        /// <param name="user">用户实体</param>
        /// <param name="query">查询条件</param>
        /// <returns>带统计数据的用户DTO</returns>
        private async Task<SysUserWithStatisticsDto> BuildUserWithStatisticsAsync(SysUser user, SubordinateQueryDto query)
        {
            // 获取上级用户信息
            string? parentUserName = null;
            if (!string.IsNullOrEmpty(user.ParentUserId))
            {
                var parentUser = await _userDLL.GetByIdAsync(user.ParentUserId);
                parentUserName = parentUser?.RealName;
            }

            // 获取直接下级数量
            var directSubordinateCount = await _userDLL.GetDirectSubordinateCountAsync(user.UserId);

            // 获取管理的普通用户数量
            var managedUserCount = await _userDLL.GetManagedUserCountAsync(user.UserId);

            // 获取所有管理的普通用户ID列表
            var managedUserIds = await _userDLL.GetAllManagedUserIdsAsync(user.UserId);

            // 计算统计数据
            var statistics = await CalculateUserStatisticsAsync(managedUserIds, query.StartTime, query.EndTime);

            return new SysUserWithStatisticsDto
            {
                Id = user.UserId,
                UserName = user.UserName,
                RealName = user.RealName,
                Avatar = user.Avatar,
                Email = user.Email,
                Mobile = user.Mobile,
                Status = user.Status,
                LastLoginTime = user.LastLoginTime,
                LastLoginIp = user.LastLoginIp ?? string.Empty,
                CreateTime = user.CreateTime,
                UpdateTime = user.UpdateTime,
                UserType = user.UserType,
                ParentUserId = user.ParentUserId,
                ParentUserName = parentUserName,
                DirectSubordinateCount = directSubordinateCount,
                TotalSubordinateUserCount = managedUserCount,
                Statistics = statistics
            };
        }

        /// <summary>
        /// 计算用户统计数据
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>统计数据汇总</returns>
        private async Task<SysUserStatisticsSummaryDto> CalculateUserStatisticsAsync(List<string> userIds, DateTime? startTime, DateTime? endTime)
        {
            if (userIds.Count == 0)
            {
                return new SysUserStatisticsSummaryDto
                {
                    StatisticsStartTime = startTime,
                    StatisticsEndTime = endTime
                };
            }

            // 使用实时统计服务计算统计数据
            _ = new UserDailyStatisticsQueryDto
            {
                UserIds = userIds,
                StartDate = startTime,
                EndDate = endTime,
                PageIndex = 1,
                PageSize = int.MaxValue
            };

            // 创建一个临时的当前用户信息（用于统计服务）
            _ = new CurrentUserInfoDto
            {
                UserId = "system",
                UserName = "system",
                UserType = 1 // 超级管理员权限
            };

            // TODO: 使用新的UserBatchRecordService实现用户统计
            _ = new { Items = new List<object>() };

            // 聚合统计数据
            var totalViewCount = 0;
            var completeViewCount = 0;
            var totalAnswerCount = 0;
            var correctAnswerCount = 0;
            var totalRewardAmount = 0m;
            var rewardClaimCount = 0;

            // TODO: 使用新的UserBatchRecordService实现统计聚合
            // foreach (var stat in userStats.Items)
            // {
            //     totalViewCount += stat.ViewCount;
            //     completeViewCount += stat.CompleteViewCount;
            //     totalAnswerCount += stat.AnswerCount;
            //     correctAnswerCount += stat.CorrectAnswerCount;
            //     totalRewardAmount += stat.RewardAmount;
            //     rewardClaimCount += stat.RewardCount;
            // }

            // 计算比率
            var completeRate = totalViewCount > 0 ? (decimal)completeViewCount / totalViewCount * 100 : 0;
            var correctRate = totalAnswerCount > 0 ? (decimal)correctAnswerCount / totalAnswerCount * 100 : 0;

            return new SysUserStatisticsSummaryDto
            {
                TotalViewCount = totalViewCount,
                CompleteViewCount = completeViewCount,
                TotalAnswerCount = totalAnswerCount,
                CorrectAnswerCount = correctAnswerCount,
                TotalRewardAmount = totalRewardAmount,
                RewardClaimCount = rewardClaimCount,
                CompleteRate = completeRate,
                CorrectRate = correctRate,
                StatisticsStartTime = startTime,
                StatisticsEndTime = endTime
            };
        }

        /// <summary>
        /// 验证下级访问权限
        /// </summary>
        /// <param name="targetUserId">目标用户ID</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns></returns>
        private async Task ValidateSubordinateAccessPermissionAsync(string targetUserId, CurrentUserInfoDto currentUserInfo)
        {
            // 超级管理员可以查看所有用户的下级
            if (currentUserInfo.UserType == 1)
                return;

            // 用户可以查看自己的下级
            if (currentUserInfo.UserId == targetUserId)
                return;

            // 检查目标用户是否是当前用户的下级
            var targetUser = await _userDLL.GetByIdAsync(targetUserId);
            if (targetUser == null)
                throw new BusinessException("指定的用户不存在");

            // 获取当前用户的所有下级ID
            var subordinateIds = await _userDLL.GetAllSubordinateUserIdsAsync(currentUserInfo.UserId);

            if (!subordinateIds.Contains(targetUserId))
                throw new BusinessException("权限不足，您只能查看自己或下级用户的信息");
        }

        #endregion


    }
}