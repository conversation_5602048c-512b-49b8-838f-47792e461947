using BLL.VideoService;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceVideoSharing.Controllers.Attributes;
using ServiceVideoSharing.Controllers.BasisController;
using ServiceVideoSharing.Controllers.BasisController.ResuItEntity;
using System.Text.Json;

namespace ServiceVideoSharing.Controllers.VideoControllers
{
    /// <summary>
    /// 视频管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Permission]
    public class VideoController(VideoService videoService, VideoProcessingService videoProcessingService) : BaseController
    {
        private readonly VideoService _videoService = videoService;
        private readonly VideoProcessingService _videoProcessingService = videoProcessingService;

        /// <summary>
        /// 添加视频
        /// </summary>
        /// <param name="createDto">创建视频DTO</param>
        /// <returns>视频ID</returns>
        [HttpPost(Name = "Video_Add")]
        public async Task<Result<int>> Add([FromBody] VideoCreateDto createDto)
        {
            var videoId = await _videoService.CreateVideoAsync(createDto, GetCurrentUserInfo());
            return Success(videoId, "视频添加成功");
        }

        /// <summary>
        /// 上传视频文件
        /// </summary>
        /// <param name="uploadDto">上传视频DTO</param>
        /// <returns>上传结果</returns>
        [HttpPost("upload", Name = "Video_Upload")]
        [RequestSizeLimit(2147483648)] // 2GB
        [RequestFormLimits(MultipartBodyLengthLimit = 2147483648)]
        public async Task<Result<VideoUploadResponseDto>> UploadVideo([FromForm] VideoUploadDto uploadDto)
        {
            try
            {
                var result = await _videoProcessingService.ProcessVideoUploadAsync(uploadDto);
                return Success(result, "视频上传成功");
            }
            catch (Exception ex)
            {
                return Fail<VideoUploadResponseDto>($"视频上传失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 简单视频上传（兼容性接口）
        /// </summary>
        /// <param name="videoFile">视频文件</param>
        /// <returns>上传结果</returns>
        [HttpPost("upload-simple", Name = "Video_UploadSimple")]
        [RequestSizeLimit(2147483648)] // 2GB
        [RequestFormLimits(MultipartBodyLengthLimit = 2147483648)]
        public async Task<Result<string>> UploadVideoSimple(IFormFile videoFile)
        {
            try
            {
                var uploadDto = new VideoUploadDto
                {
                    VideoFile = videoFile,
                    EnableCompression = false
                };

                var result = await _videoProcessingService.ProcessVideoUploadAsync(uploadDto);
                return Success(result.OriginalVideoUrl, "视频上传成功");
            }
            catch (Exception ex)
            {
                return Fail<string>($"视频上传失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 完整视频上传（一次性上传视频文件并创建视频记录）
        /// </summary>
        /// <param name="uploadDto">完整上传DTO</param>
        /// <returns>上传结果</returns>
        [HttpPost("upload-complete", Name = "Video_UploadComplete")]
        [RequestSizeLimit(2147483648)] // 2GB
        [RequestFormLimits(MultipartBodyLengthLimit = 2147483648)]
        public async Task<Result<VideoCompleteUploadResponseDto>> UploadVideoComplete([FromForm] VideoCompleteUploadDto uploadDto)
        {
            try
            {
                // 记录接收到的数据
                Console.WriteLine($"接收到视频上传请求 - 标题: {uploadDto.Title}, 奖励金额: {uploadDto.RewardAmount}");
                // 1. 验证输入数据
                if (!ModelState.IsValid)
                {
                    var errors = ModelState
                        .Where(x => x.Value?.Errors.Count > 0)
                        .Select(x => $"{x.Key}: {string.Join(", ", x.Value?.Errors.Select(e => e.ErrorMessage) ?? new List<string>())}")
                        .ToList();
                    return Fail<VideoCompleteUploadResponseDto>($"输入数据验证失败: {string.Join("; ", errors)}");
                }

                // 2. 上传并处理视频文件
                var videoUploadResult = await _videoProcessingService.ProcessVideoUploadAsync(new VideoUploadDto
                {
                    VideoFile = uploadDto.VideoFile,
                    EnableCompression = uploadDto.EnableCompression,
                    CompressionQuality = uploadDto.CompressionQuality
                });

                // 3. 处理封面（优先使用CoverUrl，否则使用CoverFile）
                string? coverUrl = null;
                if (!string.IsNullOrEmpty(uploadDto.CoverUrl))
                {
                    // 如果提供了CoverUrl，直接使用
                    coverUrl = uploadDto.CoverUrl;
                    Console.WriteLine($"使用提供的封面URL: {coverUrl}");
                }
                else if (uploadDto.CoverFile != null)
                {
                    // 如果没有CoverUrl但有CoverFile，处理文件上传
                    coverUrl = await _videoProcessingService.ProcessCoverUploadAsync(uploadDto.CoverFile);
                    Console.WriteLine($"处理封面文件上传，生成URL: {coverUrl}");
                }

                // 4. 解析问题数据
                List<VideoQuestionDto>? questions = null;
                if (!string.IsNullOrEmpty(uploadDto.QuestionsJson))
                {
                    questions = System.Text.Json.JsonSerializer.Deserialize<List<VideoQuestionDto>>(uploadDto.QuestionsJson);
                }

                // 5. 从Token中获取当前用户ID
                Console.WriteLine("开始获取当前用户信息...");
                var currentUserInfo = GetCurrentUserInfo();
                var currentUserId = currentUserInfo.UserId;
                Console.WriteLine($"获取到用户信息 - ID: {currentUserId}, 用户名: {currentUserInfo.UserName}, 类型: {currentUserInfo.UserType}");

                // 6. 创建视频记录
                var createDto = new VideoCreateDto
                {
                    Title = uploadDto.Title,
                    Description = uploadDto.Description,
                    VideoUrl = uploadDto.EnableCompression ?
                        $"/videos/compressed/{DateTime.Now:yyyy-MM-dd}/{videoUploadResult.FileId}_compressed{Path.GetExtension(videoUploadResult.OriginalVideoUrl)}" :
                        videoUploadResult.OriginalVideoUrl,
                    CoverUrl = coverUrl,
                    Duration = videoUploadResult.Duration,
                    RewardAmount = uploadDto.RewardAmount,
                    Questions = questions,
                    FileId = uploadDto.EnableCompression ? videoUploadResult.FileId : null
                };

                var videoId = await _videoService.CreateVideoAsync(createDto, GetCurrentUserInfo());

                // 6. 返回完整结果
                return Success(new VideoCompleteUploadResponseDto
                {
                    VideoId = videoId,
                    VideoUrl = videoUploadResult.OriginalVideoUrl,
                    CoverUrl = coverUrl,
                    Duration = videoUploadResult.Duration,
                    FileSize = videoUploadResult.OriginalFileSize,
                    VideoFormat = videoUploadResult.VideoFormat,
                    Resolution = videoUploadResult.Resolution,
                    Success = true,
                    Message = "视频上传成功"
                }, "视频上传成功");
            }
            catch (Exception ex)
            {
                return Fail<VideoCompleteUploadResponseDto>($"视频上传失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新视频信息
        /// </summary>
        /// <param name="updateDto">更新视频DTO</param>
        /// <returns>是否成功</returns>
        [HttpPost("update", Name = "Video_Update")]
        public async Task<Result<bool>> Update([FromBody] VideoUpdateDto updateDto)
        {
            var result = await _videoService.UpdateVideoAsync(updateDto, GetCurrentUserInfo());
            return Success(result, "视频更新成功");
        }

        /// <summary>
        /// 删除视频
        /// </summary>
        /// <param name="videoId">视频ID</param>
        /// <returns>是否成功</returns>
        [HttpDelete("Delete/{videoId}")]
        public async Task<Result<bool>> Delete(int videoId)
        {
            var result = await _videoService.DeleteVideoAsync(videoId, GetCurrentUserInfo());
            return Success(result, "视频删除成功");
        }

        /// <summary>
        /// 获取视频详情
        /// </summary>
        /// <param name="videoId">视频ID</param>
        /// <returns>视频详情</returns>
        [HttpGet("Get/{videoId}")]
        [AllowAnonymous]
        public async Task<Result<VideoResponseDto?>> Get(int videoId)
        {
            var video = await _videoService.GetVideoAsync(videoId);
            return Success(video);
        }

        /// <summary>
        /// 分页查询视频列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>视频列表</returns>
        [HttpGet(Name = "Video_GetList")]
        public async Task<Result<PagedResult<VideoResponseDto>>> GetList([FromQuery] VideoQueryDto queryDto)
        {
            // 获取当前用户信息并应用权限过滤
            var currentUserInfo = GetCurrentJwtUserInfo();
            var result = await _videoService.GetVideoPagedListAsync(queryDto, currentUserInfo);
            return Success(result);
        }

        /// <summary>
        /// 获取创建者的视频列表
        /// </summary>
        /// <param name="createdBy">创建者ID</param>
        /// <param name="status">状态筛选</param>
        /// <returns>视频列表</returns>
        [HttpGet("creator/{createdBy}", Name = "Video_GetCreatorVideos")]
        public async Task<Result<List<VideoResponseDto>>> GetCreatorVideos(string createdBy, [FromQuery] byte? status = null)
        {
            // 获取当前用户信息并应用权限验证
            var currentUserInfo = GetCurrentJwtUserInfo();
            var videos = await _videoService.GetVideosByCreatorAsync(createdBy, status, currentUserInfo);
            return Success(videos);
        }

        /// <summary>
        /// 获取我的视频列表
        /// </summary>
        /// <param name="status">状态筛选</param>
        /// <returns>视频列表</returns>
        [HttpGet("my-videos", Name = "Video_GetMyVideos")]
        public async Task<Result<List<VideoResponseDto>>> GetMyVideos([FromQuery] byte? status = null)
        {
            var currentUserInfo = GetCurrentJwtUserInfo();
            var videos = await _videoService.GetVideosByCreatorAsync(currentUserInfo.UserId, status, currentUserInfo);
            return Success(videos);
        }

        /// <summary>
        /// 更新视频状态
        /// </summary>
        /// <param name="videoId">视频ID</param>
        /// <param name="statusDto">状态更新DTO</param>
        /// <returns>是否成功</returns>
        [HttpPost("{videoId}/status", Name = "Video_UpdateVideoStatus")]
        public async Task<Result<bool>> UpdateVideoStatus(int videoId, [FromBody] VideoStatusUpdateDto statusDto)
        {
            // 获取当前用户信息，权限验证在Service层处理
            var currentUserInfo = GetCurrentJwtUserInfo();

            // 员工不能更新视频状态
            if (currentUserInfo.UserType == 3)
            {
                return Fail<bool>("员工无权限更新视频状态");
            }

            var result = await _videoService.UpdateVideoStatusAsync(videoId, statusDto.Status, GetCurrentUserInfo());
            return Success(result, "视频状态更新成功");
        }

        /// <summary>
        /// 搜索视频
        /// </summary>
        /// <param name="keyword">搜索关键词</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>搜索结果</returns>
        [HttpGet("search", Name = "Video_SearchVideos")]
        [AllowAnonymous]
        public async Task<Result<PagedResult<VideoResponseDto>>> SearchVideos([FromQuery] string keyword, [FromQuery] int pageIndex = 1, [FromQuery] int pageSize = 20)
        {
            var queryDto = new VideoQueryDto
            {
                Title = keyword,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
            // 搜索接口允许匿名访问，不传递用户信息
            return Success(await _videoService.GetVideoPagedListAsync(queryDto));
        }

        /// <summary>
        /// 获取视频统计信息
        /// </summary>
        /// <param name="videoId">视频ID</param>
        /// <returns>视频统计</returns>
        [HttpGet("{videoId}/statistics", Name = "Video_GetVideoStatistics")]
        public async Task<Result<VideoStatisticsDto>> GetVideoStatistics(int videoId)
        {
            // 获取当前用户信息并应用权限验证
            var currentUserInfo = GetCurrentJwtUserInfo();
            var statistics = await _videoService.GetVideoStatisticsAsync(videoId, currentUserInfo);
            return Success(statistics);
        }

        /// <summary>
        /// 获取视频压缩进度
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <returns>压缩进度</returns>
        [HttpGet("compression-progress/{fileId}", Name = "Video_GetCompressionProgress")]
        public Result<object> GetCompressionProgress(string fileId)
        {
            try
            {
                var progress = _videoProcessingService.GetCompressionProgress(fileId);
                if (progress == null)
                {
                    return Fail<object>("未找到压缩任务");
                }

                var result = new
                {
                    fileId = progress.FileId,
                    progress = progress.Progress,
                    status = progress.Status,
                    errorMessage = progress.ErrorMessage,
                    originalSize = progress.OriginalSize,
                    compressedSize = progress.CompressedSize,
                    compressedUrl = progress.CompressedUrl,
                    startTime = progress.StartTime,
                    endTime = progress.EndTime,
                    compressionRatio = progress.CompressedSize > 0 ?
                        Math.Round((double)progress.CompressedSize / progress.OriginalSize * 100, 1) : 0
                };
                return Success((object)result, "获取压缩进度成功");
            }
            catch (Exception ex)
            {
                return Fail<object>($"获取压缩进度失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 修复已压缩但数据库未更新的视频记录（临时接口）
        /// </summary>
        /// <returns>修复结果</returns>
        [HttpPost("fix-compressed-videos", Name = "Video_FixCompressedVideos")]
        public async Task<Result<string>> FixCompressedVideos()
        {
            try
            {
                // 检查管理员权限
                if (!IsAdmin())
                {
                    return Fail<string>("只有管理员可以执行此操作");
                }

                await FixCompressedVideosInternal();
                return Success("修复完成", "已修复所有压缩视频记录");
            }
            catch (Exception ex)
            {
                return Fail<string>($"修复失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 内部修复方法
        /// </summary>
        private async Task FixCompressedVideosInternal()
        {
            using var scope = HttpContext.RequestServices.CreateScope();
            var videoService = scope.ServiceProvider.GetRequiredService<VideoService>();
            var videoDAL = scope.ServiceProvider.GetRequiredService<DAL.VideoDAL.VideoDAL>();

            // 获取所有视频记录
            var queryable = new DAL.VideoDAL.VideoDAL.Queryable
            {
                PageIndex = 1,
                PageSize = 1000
            };
            var result = await videoDAL.GetPagedListAsync(queryable);
            var videos = result.Items ?? [];

            var wwwrootPath = Path.Combine(Environment.CurrentDirectory, "wwwroot");
            var compressedFolder = Path.Combine(wwwrootPath, "videos", "compressed");

            if (!Directory.Exists(compressedFolder))
                return;

            foreach (var video in videos)
            {
                try
                {
                    // 检查是否是原始文件URL
                    if (video.VideoUrl.Contains("/videos/original/"))
                    {
                        // 构造对应的压缩文件路径
                        var originalPath = video.VideoUrl.Replace("/", "\\");
                        if (originalPath.StartsWith("\\"))
                            originalPath = originalPath.Substring(1);

                        var fullOriginalPath = Path.Combine(wwwrootPath, originalPath);
                        var fileName = Path.GetFileNameWithoutExtension(fullOriginalPath);
                        var extension = Path.GetExtension(fullOriginalPath);

                        // 提取fileId
                        var fileId = fileName.Replace("_original", "");
                        var dateFolder = Path.GetFileName(Path.GetDirectoryName(fullOriginalPath)) ?? "";

                        var compressedFileName = $"{fileId}_compressed{extension}";
                        var compressedFilePath = Path.Combine(compressedFolder, dateFolder, compressedFileName);

                        if (System.IO.File.Exists(compressedFilePath))
                        {
                            // 压缩文件存在，更新数据库
                            var compressedUrl = $"/videos/compressed/{dateFolder}/{compressedFileName}";

                            // 解析Questions字段
                            List<VideoQuestionDto>? questions = null;
                            if (!string.IsNullOrEmpty(video.Questions))
                            {
                                try
                                {
                                    questions = JsonSerializer.Deserialize<List<VideoQuestionDto>>(video.Questions);
                                }
                                catch { }
                            }

                            // 创建更新DTO
                            var updateDto = new VideoUpdateDto
                            {
                                Id = video.Id,
                                Title = video.Title,
                                Description = video.Description,
                                VideoUrl = compressedUrl,
                                CoverUrl = video.CoverUrl,
                                Duration = video.Duration,
                                RewardAmount = video.RewardAmount,
                                Questions = questions
                            };

                            // 创建系统用户信息
                            var systemUserInfo = new CurrentUserInfoDto
                            {
                                UserId = "SYSTEM",
                                UserName = "系统修复脚本",
                                UserType = 1
                            };

                            await videoService.UpdateVideoAsync(updateDto, systemUserInfo);

                            // 删除原始文件（如果存在）
                            if (System.IO.File.Exists(fullOriginalPath))
                            {
                                System.IO.File.Delete(fullOriginalPath);
                            }
                        }
                    }
                }
                catch { }
            }
        }

    }
}
