{"openapi": "3.0.1", "info": {"title": "ServiceVideoSharing", "version": "1.0"}, "paths": {"/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "用户登录接口", "operationId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requestBody": {"description": "登录请求DTO，包含用户名和密码", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysLoginRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysLoginRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysLoginRequestDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfSysLoginResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfSysLoginResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfSysLoginResponseDto"}}}}}}}, "/api/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "用户登出接口", "operationId": "<PERSON><PERSON><PERSON><PERSON>", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Auth/userinfo": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获取当前登录用户信息接口", "operationId": "Auth_GetUserInfo", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfSysUserInfoDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfSysUserInfoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfSysUserInfoDto"}}}}}}}, "/api/Basis/UploadFileAsync": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "本地单文件上传", "operationId": "Basis_UploadFile", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Basis/UploadFilesAsync": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "本地多文件上传", "operationId": "Basis_UploadFiles", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"files": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Batch": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "添加批次", "operationId": "Batch_Add", "requestBody": {"description": "创建批次DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoBatchCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoBatchCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoBatchCreateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}}}}}, "get": {"tags": ["<PERSON><PERSON>"], "summary": "分页查询批次列表（带权限过滤）", "operationId": "Batch_GetList", "parameters": [{"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "CreatorId", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderField", "in": "query", "schema": {"type": "string"}}, {"name": "IsAsc", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoBatchResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoBatchResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoBatchResponseDto"}}}}}}}, "/api/Batch/{batchId}": {"delete": {"tags": ["<PERSON><PERSON>"], "summary": "删除批次", "operationId": "Batch_Delete", "parameters": [{"name": "batchId", "in": "path", "description": "批次ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}, "get": {"tags": ["<PERSON><PERSON>"], "summary": "获取批次详情", "operationId": "Batch_Get", "parameters": [{"name": "batchId", "in": "path", "description": "批次ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchResponseDto"}}}}}}}, "/api/Batch/{batchId}/statistics": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获取批次统计信息\r\n支持基于用户权限的数据过滤：\r\n- 超级管理员：可查看所有用户数据\r\n- 管理员：可查看所有员工及其用户的数据\r\n- 员工：只能查看自己绑定用户的数据", "operationId": "Batch_GetStatistics", "parameters": [{"name": "batchId", "in": "path", "description": "批次ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchStatisticsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchStatisticsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchStatisticsDto"}}}}}}}, "/api/Dashboard": {"get": {"tags": ["Dashboard"], "summary": "获取综合仪表板数据（主接口）\r\n返回类似截图的完整仪表板数据，包括：\r\n- 数据汇总（会员总数、今日新增会员、订单总数）\r\n- 标签统计（未指定标签用户数）\r\n- 课程统计（观看人数、完播人数、完播率）\r\n- 答题统计（答题人数、正确人数、正确率）\r\n- 红包统计（答题红包数、答题红包金额）", "parameters": [{"name": "startDate", "in": "query", "description": "开始时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "description": "结束时间", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoDashboardDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoDashboardDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoDashboardDto"}}}}}}}, "/api/Dashboard/summary": {"get": {"tags": ["Dashboard"], "summary": "获取数据汇总\r\n包括会员总数、今日新增会员、订单总数等关键指标", "parameters": [{"name": "startDate", "in": "query", "description": "开始时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "description": "结束时间", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoDashboardSummaryDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoDashboardSummaryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoDashboardSummaryDto"}}}}}}}, "/api/Dashboard/tags": {"get": {"tags": ["Dashboard"], "summary": "获取标签统计\r\n包括各标签的用户数量和未指定标签的用户数量", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoTagStatisticsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoTagStatisticsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoTagStatisticsDto"}}}}}}}, "/api/Dashboard/course": {"get": {"tags": ["Dashboard"], "summary": "获取课程统计\r\n包括观看人数、完播人数、完播率等", "parameters": [{"name": "startDate", "in": "query", "description": "开始时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "description": "结束时间", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoCourseStatisticsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoCourseStatisticsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoCourseStatisticsDto"}}}}}}}, "/api/Dashboard/answer": {"get": {"tags": ["Dashboard"], "summary": "获取答题统计\r\n包括答题人数、正确人数、正确率等", "parameters": [{"name": "startDate", "in": "query", "description": "开始时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "description": "结束时间", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoAnswerStatisticsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoAnswerStatisticsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoAnswerStatisticsDto"}}}}}}}, "/api/Dashboard/reward": {"get": {"tags": ["Dashboard"], "summary": "获取红包统计\r\n包括红包数量、红包金额等", "parameters": [{"name": "startDate", "in": "query", "description": "开始时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "description": "结束时间", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoRewardStatisticsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoRewardStatisticsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoRewardStatisticsDto"}}}}}}}, "/api/Dashboard/order": {"get": {"tags": ["Dashboard"], "summary": "获取订单统计\r\n包括订单数量、订单金额等", "parameters": [{"name": "startDate", "in": "query", "description": "开始时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "description": "结束时间", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoOrderStatisticsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoOrderStatisticsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoOrderStatisticsDto"}}}}}}}, "/api/Dashboard/today": {"get": {"tags": ["Dashboard"], "summary": "获取今日数据快照\r\n快速获取今日的所有关键数据", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoDashboardDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoDashboardDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoDashboardDto"}}}}}}}, "/api/Dashboard/thisMonth": {"get": {"tags": ["Dashboard"], "summary": "获取本月数据快照\r\n快速获取本月的所有关键数据", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoDashboardDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoDashboardDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoDashboardDto"}}}}}}}, "/api/Dashboard/metrics": {"get": {"tags": ["Dashboard"], "summary": "获取关键指标摘要\r\n提供最核心的几个指标数据", "parameters": [{"name": "startDate", "in": "query", "description": "开始时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "description": "结束时间", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfKeyMetricsSummaryDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfKeyMetricsSummaryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfKeyMetricsSummaryDto"}}}}}}}, "/api/Dashboard/compare/today-yesterday": {"get": {"tags": ["Dashboard"], "summary": "获取数据对比（今日vs昨日）\r\n提供今日和昨日的数据对比", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfObject"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfObject"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfObject"}}}}}}}, "/api/Log/query": {"get": {"tags": ["Log"], "summary": "分页查询日志列表", "operationId": "Log_Query", "parameters": [{"name": "Id", "in": "query", "schema": {"type": "string"}}, {"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "Username", "in": "query", "schema": {"type": "string"}}, {"name": "Operation", "in": "query", "schema": {"type": "string"}}, {"name": "Method", "in": "query", "schema": {"type": "string"}}, {"name": "LogType", "in": "query", "schema": {"type": "string"}}, {"name": "LogLevel", "in": "query", "schema": {"type": "string"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderByCreateTime", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfPageEntityOfSysLog"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfPageEntityOfSysLog"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfPageEntityOfSysLog"}}}}}}}, "/api/Log/{id}": {"get": {"tags": ["Log"], "summary": "获取日志详情", "operationId": "Log_Get", "parameters": [{"name": "id", "in": "path", "description": "日志ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfSysLog"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfSysLog"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfSysLog"}}}}}}}, "/api/Log/export": {"post": {"tags": ["Log"], "summary": "导出日志列表", "operationId": "Log_Export", "parameters": [{"name": "Id", "in": "query", "schema": {"type": "string"}}, {"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "Username", "in": "query", "schema": {"type": "string"}}, {"name": "Operation", "in": "query", "schema": {"type": "string"}}, {"name": "Method", "in": "query", "schema": {"type": "string"}}, {"name": "LogType", "in": "query", "schema": {"type": "string"}}, {"name": "LogLevel", "in": "query", "schema": {"type": "string"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderByCreateTime", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "导出配置", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Log/clear": {"post": {"tags": ["Log"], "summary": "清理指定日期之前的日志", "requestBody": {"description": "清理日志参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LogClearDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LogClearDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LogClearDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Statistics/user-daily": {"get": {"tags": ["Statistics"], "summary": "获取用户每日统计数据（分页）", "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "EmployeeId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UserIds", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderField", "in": "query", "schema": {"type": "string"}}, {"name": "IsAsc", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoUserDailyStatisticsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoUserDailyStatisticsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoUserDailyStatisticsDto"}}}}}}}, "/api/Statistics/user-summary/{userId}": {"get": {"tags": ["Statistics"], "summary": "获取用户统计汇总数据", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}, {"name": "startDate", "in": "query", "description": "开始日期", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "description": "结束日期", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoUserStatisticsSummaryDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoUserStatisticsSummaryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoUserStatisticsSummaryDto"}}}}}}}, "/api/Statistics/users-summary": {"get": {"tags": ["Statistics"], "summary": "批量获取用户统计汇总数据", "parameters": [{"name": "userIds", "in": "query", "description": "用户ID列表（逗号分隔）", "schema": {"type": "string"}}, {"name": "startDate", "in": "query", "description": "开始日期", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "description": "结束日期", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoUserStatisticsSummaryDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoUserStatisticsSummaryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoUserStatisticsSummaryDto"}}}}}}}, "/api/Statistics/employee-summary/{employeeId}": {"get": {"tags": ["Statistics"], "summary": "获取员工负责用户的统计汇总", "parameters": [{"name": "employeeId", "in": "path", "description": "员工ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "startDate", "in": "query", "description": "开始日期", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "description": "结束日期", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoUserStatisticsSummaryDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoUserStatisticsSummaryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoUserStatisticsSummaryDto"}}}}}}}, "/api/Statistics/daily-trend": {"get": {"tags": ["Statistics"], "summary": "获取每日统计趋势", "parameters": [{"name": "userIds", "in": "query", "description": "用户ID列表（逗号分隔，可选）", "schema": {"type": "string"}}, {"name": "employeeId", "in": "query", "description": "员工ID（可选）", "schema": {"type": "integer", "format": "int32"}}, {"name": "startDate", "in": "query", "description": "开始日期", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "description": "结束日期", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoDailyStatisticsTrendDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoDailyStatisticsTrendDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoDailyStatisticsTrendDto"}}}}}}}, "/api/Statistics/overview": {"get": {"tags": ["Statistics"], "summary": "获取统计概览数据（用于仪表板）", "parameters": [{"name": "startDate", "in": "query", "description": "开始日期", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "description": "结束日期", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoStatisticsOverviewDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoStatisticsOverviewDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoStatisticsOverviewDto"}}}}}}}, "/api/Statistics/user/{userId}": {"get": {"tags": ["Statistics"], "summary": "获取用户统计数据（兼容性接口）", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}, {"name": "startDate", "in": "query", "description": "开始日期", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "description": "结束日期", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoStatisticsResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoStatisticsResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoStatisticsResponseDto"}}}}}}}, "/api/Statistics/my-statistics": {"get": {"tags": ["Statistics"], "summary": "获取我的统计数据（兼容性接口）", "parameters": [{"name": "startDate", "in": "query", "description": "开始日期", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "description": "结束日期", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoStatisticsResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoStatisticsResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoStatisticsResponseDto"}}}}}}}, "/api/Statistics/summary": {"post": {"tags": ["Statistics"], "summary": "获取统计汇总数据（兼容性接口）", "requestBody": {"description": "汇总查询DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoStatisticsSummaryQueryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoStatisticsSummaryQueryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoStatisticsSummaryQueryDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoStatisticsSummaryDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoStatisticsSummaryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoStatisticsSummaryDto"}}}}}}}, "/api/Statistics/dashboard": {"get": {"tags": ["Statistics"], "summary": "获取仪表板数据", "parameters": [{"name": "startDate", "in": "query", "description": "开始日期", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "description": "结束日期", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoDashboardDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoDashboardDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoDashboardDto"}}}}}}}, "/api/Statistics/dashboard/key-metrics": {"get": {"tags": ["Statistics"], "summary": "获取仪表板关键指标汇总", "parameters": [{"name": "startDate", "in": "query", "description": "开始日期", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "description": "结束日期", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfKeyMetricsSummaryDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfKeyMetricsSummaryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfKeyMetricsSummaryDto"}}}}}}}, "/api/Statistics/dashboard/today": {"get": {"tags": ["Statistics"], "summary": "获取今日数据快照", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoDashboardDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoDashboardDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoDashboardDto"}}}}}}}, "/api/SystemConfig": {"post": {"tags": ["SystemConfig"], "summary": "添加系统配置", "operationId": "SystemConfig_Add", "requestBody": {"description": "创建系统配置DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoSystemConfigCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoSystemConfigCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoSystemConfigCreateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}}}}}, "get": {"tags": ["SystemConfig"], "summary": "分页查询系统配置列表", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "ConfigType", "in": "query", "schema": {"type": "string"}}, {"name": "GroupName", "in": "query", "schema": {"type": "string"}}, {"name": "IsEnabled", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderField", "in": "query", "schema": {"type": "string"}}, {"name": "IsAsc", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoSystemConfigResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoSystemConfigResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoSystemConfigResponseDto"}}}}}}}, "/api/SystemConfig/update": {"post": {"tags": ["SystemConfig"], "summary": "更新系统配置", "operationId": "SystemConfig_Update", "requestBody": {"description": "更新系统配置DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoSystemConfigUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoSystemConfigUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoSystemConfigUpdateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/SystemConfig/{configId}": {"delete": {"tags": ["SystemConfig"], "summary": "删除系统配置", "operationId": "SystemConfig_Delete", "parameters": [{"name": "configId", "in": "path", "description": "配置ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}, "get": {"tags": ["SystemConfig"], "summary": "获取系统配置详情", "parameters": [{"name": "configId", "in": "path", "description": "配置ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoSystemConfigResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoSystemConfigResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoSystemConfigResponseDto"}}}}}}}, "/api/SystemConfig/key/{configKey}": {"get": {"tags": ["SystemConfig"], "summary": "根据配置键获取配置", "parameters": [{"name": "config<PERSON><PERSON>", "in": "path", "description": "配置键", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoSystemConfigResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoSystemConfigResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoSystemConfigResponseDto"}}}}}}}, "/api/SystemConfig/value/{configKey}": {"get": {"tags": ["SystemConfig"], "summary": "获取配置值", "parameters": [{"name": "config<PERSON><PERSON>", "in": "path", "description": "配置键", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfString"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfString"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfString"}}}}}}, "post": {"tags": ["SystemConfig"], "summary": "设置配置值", "parameters": [{"name": "config<PERSON><PERSON>", "in": "path", "description": "配置键", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "配置值", "content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/SystemConfig/all": {"get": {"tags": ["SystemConfig"], "summary": "获取所有系统配置", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoSystemConfigResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoSystemConfigResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoSystemConfigResponseDto"}}}}}}}, "/api/SystemConfig/group/{groupName}": {"get": {"tags": ["SystemConfig"], "summary": "根据分组获取配置", "parameters": [{"name": "groupName", "in": "path", "description": "配置分组", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoSystemConfigResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoSystemConfigResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoSystemConfigResponseDto"}}}}}}}, "/api/SystemConfig/type/{configType}": {"get": {"tags": ["SystemConfig"], "summary": "根据配置类型获取配置", "parameters": [{"name": "configType", "in": "path", "description": "配置类型", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoSystemConfigResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoSystemConfigResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoSystemConfigResponseDto"}}}}}}}, "/api/SystemConfig/batch-update": {"post": {"tags": ["SystemConfig"], "summary": "批量更新配置", "requestBody": {"description": "配置更新字典", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/SystemConfig/groups": {"get": {"tags": ["SystemConfig"], "summary": "获取所有配置分组", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfString"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfString"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfString"}}}}}}}, "/api/SystemConfig/wechat": {"get": {"tags": ["SystemConfig"], "summary": "获取微信相关配置", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfDictionaryOfStringString"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfDictionaryOfStringString"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfDictionaryOfStringString"}}}}}}}, "/api/SystemConfig/reward": {"get": {"tags": ["SystemConfig"], "summary": "获取红包相关配置", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfDictionaryOfStringString"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfDictionaryOfStringString"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfDictionaryOfStringString"}}}}}}}, "/api/SystemConfig/system": {"get": {"tags": ["SystemConfig"], "summary": "获取系统相关配置", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfDictionaryOfStringString"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfDictionaryOfStringString"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfDictionaryOfStringString"}}}}}}}, "/api/SystemConfig/{configId}/status": {"post": {"tags": ["SystemConfig"], "summary": "启用/禁用配置", "parameters": [{"name": "configId", "in": "path", "description": "配置ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "是否启用", "content": {"application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}, "application/*+json": {"schema": {"type": "boolean"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/SysUser/create": {"post": {"tags": ["SysUser"], "summary": "创建新用户", "operationId": "SysUser_Create", "requestBody": {"description": "创建用户请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysCreateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysCreateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysCreateUserDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfString"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfString"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfString"}}}}}}}, "/api/SysUser/update": {"post": {"tags": ["SysUser"], "summary": "更新用户信息", "operationId": "SysUser_Update", "requestBody": {"description": "更新用户请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysUpdateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysUpdateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysUpdateUserDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/SysUser/delete/{id}": {"post": {"tags": ["SysUser"], "summary": "删除指定用户", "operationId": "SysUser_Delete", "parameters": [{"name": "id", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/SysUser/{id}": {"get": {"tags": ["SysUser"], "summary": "获取用户详细信息", "operationId": "SysUser_Get", "parameters": [{"name": "id", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfSysUserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfSysUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfSysUserDto"}}}}}}}, "/api/SysUser/change-password": {"post": {"tags": ["SysUser"], "summary": "修改用户密码", "operationId": "SysUser_ChangePassword", "requestBody": {"description": "修改密码请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysChangePasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysChangePasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysChangePasswordDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/SysUser/reset-password": {"post": {"tags": ["SysUser"], "summary": "重置用户密码", "operationId": "SysUser_ResetPassword", "requestBody": {"description": "重置密码请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysResetPasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysResetPasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysResetPasswordDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/SysUser/administrators": {"get": {"tags": ["SysUser"], "summary": "获取管理员列表（带统计数据）", "operationId": "SysUser_GetAdministrators", "parameters": [{"name": "startTime", "in": "query", "description": "统计开始时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "endTime", "in": "query", "description": "统计结束时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "pageIndex", "in": "query", "description": "页码，默认为1", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "description": "每页大小，默认为20", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "userName", "in": "query", "description": "用户名（模糊查询）", "schema": {"type": "string"}}, {"name": "realName", "in": "query", "description": "真实姓名（模糊查询）", "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "用户状态过滤", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfSysUserWithStatisticsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfSysUserWithStatisticsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfSysUserWithStatisticsDto"}}}}}}}, "/api/SysUser/employees": {"get": {"tags": ["SysUser"], "summary": "获取员工列表（带统计数据）", "operationId": "SysUser_GetEmployees", "parameters": [{"name": "startTime", "in": "query", "description": "统计开始时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "endTime", "in": "query", "description": "统计结束时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "pageIndex", "in": "query", "description": "页码，默认为1", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "description": "每页大小，默认为20", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "userName", "in": "query", "description": "用户名（模糊查询）", "schema": {"type": "string"}}, {"name": "realName", "in": "query", "description": "真实姓名（模糊查询）", "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "用户状态过滤", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfSysUserWithStatisticsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfSysUserWithStatisticsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfSysUserWithStatisticsDto"}}}}}}}, "/api/SysUser/subordinates": {"get": {"tags": ["SysUser"], "summary": "获取当前用户的下级列表（带统计数据）", "operationId": "SysUser_GetSubordinates", "parameters": [{"name": "startTime", "in": "query", "description": "统计开始时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "endTime", "in": "query", "description": "统计结束时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "pageIndex", "in": "query", "description": "页码，默认为1", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "description": "每页大小，默认为20", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "userName", "in": "query", "description": "用户名（模糊查询）", "schema": {"type": "string"}}, {"name": "realName", "in": "query", "description": "真实姓名（模糊查询）", "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "用户状态过滤", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfSysUserWithStatisticsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfSysUserWithStatisticsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfSysUserWithStatisticsDto"}}}}}}}, "/api/SysUser/subordinates/{id}": {"get": {"tags": ["SysUser"], "summary": "获取指定用户的下级列表（带统计数据）", "operationId": "SysUser_GetSubordinatesById", "parameters": [{"name": "id", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}, {"name": "startTime", "in": "query", "description": "统计开始时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "endTime", "in": "query", "description": "统计结束时间", "schema": {"type": "string", "format": "date-time"}}, {"name": "pageIndex", "in": "query", "description": "页码，默认为1", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "description": "每页大小，默认为20", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "userName", "in": "query", "description": "用户名（模糊查询）", "schema": {"type": "string"}}, {"name": "realName", "in": "query", "description": "真实姓名（模糊查询）", "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "用户状态过滤", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfSysUserWithStatisticsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfSysUserWithStatisticsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfSysUserWithStatisticsDto"}}}}}}}, "/api/User/login": {"post": {"tags": ["User"], "summary": "微信登录/注册（自动注册新用户）", "operationId": "VideoUser_Login", "requestBody": {"description": "微信登录信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoWechatLoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoWechatLoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoWechatLoginDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoLoginResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoLoginResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoLoginResponseDto"}}}}}}}, "/api/User/profile": {"get": {"tags": ["User"], "summary": "获取当前用户信息", "operationId": "VideoUser_GetProfile", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoUserResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoUserResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoUserResponseDto"}}}}}}}, "/api/User/{userId}": {"get": {"tags": ["User"], "summary": "获取用户详情", "operationId": "VideoUser_GetUser", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoUserResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoUserResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoUserResponseDto"}}}}}}}, "/api/User": {"get": {"tags": ["User"], "summary": "分页查询用户列表（带权限过滤）", "operationId": "VideoUser_GetUserPagedList", "parameters": [{"name": "Nickname", "in": "query", "schema": {"type": "string"}}, {"name": "EmployeeId", "in": "query", "schema": {"type": "string"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderField", "in": "query", "schema": {"type": "string"}}, {"name": "IsAsc", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoUserResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoUserResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoUserResponseDto"}}}}}}}, "/api/User/transfer": {"post": {"tags": ["User"], "summary": "转移用户到新员工（带权限验证）", "operationId": "VideoUser_TransferUsers", "requestBody": {"description": "转移DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoUserTransferDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoUserTransferDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoUserTransferDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/User/access-promotion": {"post": {"tags": ["User"], "summary": "访问推广链接", "operationId": "VideoUser_AccessPromotion", "requestBody": {"description": "访问推广链接DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoAccessPromotionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoAccessPromotionDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoAccessPromotionDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoAccessPromotionResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoAccessPromotionResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoAccessPromotionResponseDto"}}}}}}}, "/api/UserAudit/audit-user/{userId}": {"post": {"tags": ["UserAudit"], "summary": "员工审核用户", "operationId": "UserAudit_AuditUser", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "审核信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoUserAuditDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoUserAuditDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoUserAuditDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/UserAudit/pending-users": {"get": {"tags": ["UserAudit"], "summary": "获取当前员工的待审核用户列表", "operationId": "UserAudit_GetPendingUsers", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoUserResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoUserResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoUserResponseDto"}}}}}}}, "/api/UserBatchRecord/create-or-get": {"post": {"tags": ["UserBatchRecord"], "summary": "创建或获取用户批次记录\r\n用户进入视频页面时调用，确保有记录存在", "operationId": "UserBatchRecord_CreateOrGet", "requestBody": {"description": "创建记录DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoUserBatchRecordCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoUserBatchRecordCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoUserBatchRecordCreateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoUserBatchRecordResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoUserBatchRecordResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoUserBatchRecordResponseDto"}}}}}}}, "/api/UserBatchRecord/{userId}/{batchId}": {"get": {"tags": ["UserBatchRecord"], "summary": "获取用户批次记录", "operationId": "UserBatchRecord_Get", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}, {"name": "batchId", "in": "path", "description": "批次ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoUserBatchRecordResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoUserBatchRecordResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoUserBatchRecordResponseDto"}}}}}}}, "/api/UserBatchRecord/user/{userId}": {"get": {"tags": ["UserBatchRecord"], "summary": "获取用户的所有批次记录", "operationId": "UserBatchRecord_GetUserRecords", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoUserBatchRecordSummaryDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoUserBatchRecordSummaryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoUserBatchRecordSummaryDto"}}}}}}}, "/api/UserBatchRecord/{userId}/watch-progress": {"post": {"tags": ["UserBatchRecord"], "summary": "更新观看进度", "operationId": "UserBatchRecord_UpdateWatchProgress", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "观看进度更新DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoWatchProgressUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoWatchProgressUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoWatchProgressUpdateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/UserBatchRecord/{userId}/{batchId}/has-watched": {"get": {"tags": ["UserBatchRecord"], "summary": "检查用户是否已观看", "operationId": "UserBatchRecord_HasWatched", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}, {"name": "batchId", "in": "path", "description": "批次ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/UserBatchRecord/{userId}/{batchId}/has-completed": {"get": {"tags": ["UserBatchRecord"], "summary": "检查用户是否完播", "operationId": "UserBatchRecord_HasCompleted", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}, {"name": "batchId", "in": "path", "description": "批次ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/UserBatchRecord/{userId}/{batchId}/watch-status": {"get": {"tags": ["UserBatchRecord"], "summary": "获取用户观看状态", "operationId": "UserBatchRecord_GetWatchStatus", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}, {"name": "batchId", "in": "path", "description": "批次ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWatchStatusDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWatchStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWatchStatusDto"}}}}}}}, "/api/UserBatchRecord/{userId}/{batchId}/start-watching": {"post": {"tags": ["UserBatchRecord"], "summary": "开始观看（记录开始时间）", "operationId": "UserBatchRecord_StartWatching", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}, {"name": "batchId", "in": "path", "description": "批次ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/UserBatchRecord/batch-update-progress": {"post": {"tags": ["UserBatchRecord"], "summary": "批量更新观看进度\r\n用于前端定时同步多个用户的观看进度", "operationId": "UserBatchRecord_BatchUpdateProgress", "requestBody": {"description": "进度更新列表", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VideoBatchWatchProgressUpdateDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VideoBatchWatchProgressUpdateDto"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VideoBatchWatchProgressUpdateDto"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}}}}}}, "/api/UserBatchRecord/{userId}/submit-answer": {"post": {"tags": ["UserBatchRecord"], "summary": "提交答题结果", "operationId": "UserBatchRecord_SubmitAnswer", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "答题结果DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoAnswerSubmitDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoAnswerSubmitDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoAnswerSubmitDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/UserBatchRecord/{userId}/{batchId}/has-answered": {"get": {"tags": ["UserBatchRecord"], "summary": "检查用户是否已答题", "operationId": "UserBatchRecord_HasAnswered", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}, {"name": "batchId", "in": "path", "description": "批次ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/UserBatchRecord/{userId}/{batchId}/answer-status": {"get": {"tags": ["UserBatchRecord"], "summary": "获取用户答题状态", "operationId": "UserBatchRecord_GetAnswerStatus", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}, {"name": "batchId", "in": "path", "description": "批次ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoAnswerStatusDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoAnswerStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoAnswerStatusDto"}}}}}}}, "/api/UserBatchRecord/{userId}/{batchId}/answer-detail": {"get": {"tags": ["UserBatchRecord"], "summary": "获取用户答题详情\r\n需要权限验证：管理员可查看所有，员工只能查看自己绑定的用户", "operationId": "UserBatchRecord_GetAnswerDetail", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}, {"name": "batchId", "in": "path", "description": "批次ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoAnswerDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoAnswerDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoAnswerDetailDto"}}}}}}}, "/api/UserBatchRecord/{userId}/{batchId}/answer-eligibility": {"get": {"tags": ["UserBatchRecord"], "summary": "验证答题资格", "operationId": "UserBatchRecord_CheckAnswerEligibility", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}, {"name": "batchId", "in": "path", "description": "批次ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoAnswerEligibilityDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoAnswerEligibilityDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoAnswerEligibilityDto"}}}}}}}, "/api/UserBatchRecord/{userId}/grant-reward": {"post": {"tags": ["UserBatchRecord"], "summary": "发放红包", "operationId": "UserBatchRecord_GrantReward", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "红包发放DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoRewardGrantDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoRewardGrantDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoRewardGrantDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/UserBatchRecord/{userId}/update-reward-status": {"post": {"tags": ["UserBatchRecord"], "summary": "更新红包状态", "operationId": "UserBatchRecord_UpdateRewardStatus", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "红包状态更新DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoRewardStatusUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoRewardStatusUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoRewardStatusUpdateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/UserBatchRecord/{userId}/{batchId}/has-reward": {"get": {"tags": ["UserBatchRecord"], "summary": "检查用户是否已获得红包", "operationId": "UserBatchRecord_HasReward", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}, {"name": "batchId", "in": "path", "description": "批次ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/UserBatchRecord/{userId}/{batchId}/reward-status": {"get": {"tags": ["UserBatchRecord"], "summary": "获取用户红包状态", "operationId": "UserBatchRecord_GetRewardStatus", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}, {"name": "batchId", "in": "path", "description": "批次ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoRewardStatusDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoRewardStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoRewardStatusDto"}}}}}}}, "/api/UserBatchRecord/{userId}/{batchId}/reward-eligibility": {"get": {"tags": ["UserBatchRecord"], "summary": "验证红包发放资格", "operationId": "UserBatchRecord_CheckRewardEligibility", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}, {"name": "batchId", "in": "path", "description": "批次ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoRewardEligibilityDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoRewardEligibilityDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoRewardEligibilityDto"}}}}}}}, "/api/UserBatchRecord/batch/{batchId}/grant-rewards": {"post": {"tags": ["UserBatchRecord"], "summary": "批量发放红包\r\n为指定批次中符合条件的所有用户发放红包", "operationId": "UserBatchRecord_BatchGrantRewards", "parameters": [{"name": "batchId", "in": "path", "description": "批次ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "红包金额", "content": {"application/json": {"schema": {"type": "number", "format": "double"}}, "text/json": {"schema": {"type": "number", "format": "double"}}, "application/*+json": {"schema": {"type": "number", "format": "double"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchRewardResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchRewardResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchRewardResultDto"}}}}}}}, "/api/UserBatchRecord/batch/{batchId}/statistics": {"get": {"tags": ["UserBatchRecord"], "summary": "获取批次统计数据\r\n支持基于用户权限的数据过滤：\r\n- 超级管理员：可查看所有用户数据\r\n- 管理员：可查看所有员工及其用户的数据\r\n- 员工：只能查看自己绑定用户的数据", "operationId": "UserBatchRecord_GetBatchStatistics", "parameters": [{"name": "batchId", "in": "path", "description": "批次ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchStatisticsFromRecordDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchStatisticsFromRecordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchStatisticsFromRecordDto"}}}}}}}, "/api/UserBatchRecord/batch/{batchId}/records": {"get": {"tags": ["UserBatchRecord"], "summary": "获取批次记录列表\r\n支持基于用户权限的数据过滤", "operationId": "UserBatchRecord_GetBatchRecords", "parameters": [{"name": "batchId", "in": "path", "description": "批次ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoUserBatchRecordResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoUserBatchRecordResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoUserBatchRecordResponseDto"}}}}}}}, "/api/ViewRecord": {"post": {"tags": ["UserBatchRecord"], "summary": "兼容旧的ViewRecord创建接口", "operationId": "ViewRecord_Create_Compat", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoUserBatchRecordCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoUserBatchRecordCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoUserBatchRecordCreateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}}}}}}, "/api/ViewRecord/update-progress": {"post": {"tags": ["UserBatchRecord"], "summary": "兼容旧的ViewRecord进度更新接口", "operationId": "ViewRecord_UpdateProgress_Compat", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoWatchProgressUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoWatchProgressUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoWatchProgressUpdateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/ViewRecord/batch/{batchId}": {"get": {"tags": ["UserBatchRecord"], "summary": "兼容旧的ViewRecord批次记录查询接口", "operationId": "ViewRecord_GetBatchRecords_Compat", "parameters": [{"name": "batchId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "pageIndex", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoUserBatchRecordResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoUserBatchRecordResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoUserBatchRecordResponseDto"}}}}}}}, "/api/ViewRecord/statistics": {"get": {"tags": ["UserBatchRecord"], "summary": "兼容旧的ViewRecord统计接口", "operationId": "ViewRecord_GetStatistics_Compat", "parameters": [{"name": "batchId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchStatisticsFromRecordDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchStatisticsFromRecordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchStatisticsFromRecordDto"}}}}}}}, "/api/AnswerRecord/submit": {"post": {"tags": ["UserBatchRecord"], "summary": "兼容旧的AnswerRecord提交接口", "operationId": "AnswerRecord_Submit_Compat", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoAnswerSubmitDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoAnswerSubmitDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoAnswerSubmitDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}}}}}}, "/api/AnswerRecord/statistics/{batchId}": {"get": {"tags": ["UserBatchRecord"], "summary": "兼容旧的AnswerRecord统计接口", "operationId": "AnswerRecord_GetStatistics_Compat", "parameters": [{"name": "batchId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchStatisticsFromRecordDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchStatisticsFromRecordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchStatisticsFromRecordDto"}}}}}}}, "/api/Reward/user/{userId}": {"get": {"tags": ["UserBatchRecord"], "summary": "兼容旧的Reward用户奖励查询接口", "operationId": "Reward_GetUserRewards_Compat", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoUserBatchRecordResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoUserBatchRecordResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoUserBatchRecordResponseDto"}}}}}}}, "/api/UserTransfer": {"post": {"tags": ["UserTransfer"], "summary": "转移用户到新员工", "operationId": "UserTransfer_Transfer", "requestBody": {"description": "用户转移DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoUserTransferDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoUserTransferDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoUserTransferDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}, "get": {"tags": ["UserTransfer"], "summary": "分页查询用户转移记录列表\r\n支持按用户ID、员工ID、操作员ID、时间范围等条件查询", "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "FromEmployeeId", "in": "query", "schema": {"type": "string"}}, {"name": "ToEmployeeId", "in": "query", "schema": {"type": "string"}}, {"name": "OperatorId", "in": "query", "schema": {"type": "string"}}, {"name": "OperatorName", "in": "query", "schema": {"type": "string"}}, {"name": "Reason", "in": "query", "schema": {"type": "string"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderField", "in": "query", "schema": {"type": "string"}}, {"name": "IsAsc", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoUserTransferResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoUserTransferResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoUserTransferResponseDto"}}}}}}}, "/api/Video": {"post": {"tags": ["Video"], "summary": "添加视频", "operationId": "Video_Add", "requestBody": {"description": "创建视频DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoVideoCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoVideoCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoVideoCreateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}}}}}, "get": {"tags": ["Video"], "summary": "分页查询视频列表", "operationId": "Video_GetList", "parameters": [{"name": "Title", "in": "query", "schema": {"type": "string"}}, {"name": "CreatedBy", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderField", "in": "query", "schema": {"type": "string"}}, {"name": "IsAsc", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoVideoResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoVideoResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoVideoResponseDto"}}}}}}}, "/api/Video/upload": {"post": {"tags": ["Video"], "summary": "上传视频文件", "operationId": "Video_Upload", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["VideoFile"], "type": "object", "properties": {"VideoFile": {"type": "string", "format": "binary"}, "EnableCompression": {"type": "boolean"}, "Title": {"maxLength": 255, "type": "string"}, "Description": {"maxLength": 1000, "type": "string"}, "CompressionQuality": {"maximum": 10, "minimum": 1, "type": "integer", "format": "int32"}, "MaxWidth": {"maximum": 1920, "minimum": 480, "type": "integer", "format": "int32"}}}, "encoding": {"VideoFile": {"style": "form"}, "EnableCompression": {"style": "form"}, "Title": {"style": "form"}, "Description": {"style": "form"}, "CompressionQuality": {"style": "form"}, "MaxWidth": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoVideoUploadResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoVideoUploadResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoVideoUploadResponseDto"}}}}}}}, "/api/Video/upload-simple": {"post": {"tags": ["Video"], "summary": "简单视频上传（兼容性接口）", "operationId": "Video_UploadSimple", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"videoFile": {"type": "string", "format": "binary"}}}, "encoding": {"videoFile": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfString"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfString"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfString"}}}}}}}, "/api/Video/upload-complete": {"post": {"tags": ["Video"], "summary": "完整视频上传（一次性上传视频文件并创建视频记录）", "operationId": "Video_UploadComplete", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["<PERSON><PERSON><PERSON>", "RewardAmount", "Title", "VideoFile"], "type": "object", "properties": {"VideoFile": {"type": "string", "format": "binary"}, "CoverFile": {"type": "string", "format": "binary"}, "CoverUrl": {"maxLength": 255, "type": "string"}, "Title": {"maxLength": 255, "type": "string"}, "Description": {"maxLength": 1000, "type": "string"}, "RewardAmount": {"minimum": 0.01, "type": "number", "format": "double"}, "QuestionsJson": {"type": "string"}, "EnableCompression": {"type": "boolean"}, "CompressionQuality": {"maximum": 10, "minimum": 1, "type": "integer", "format": "int32"}}}, "encoding": {"VideoFile": {"style": "form"}, "CoverFile": {"style": "form"}, "CoverUrl": {"style": "form"}, "Title": {"style": "form"}, "Description": {"style": "form"}, "RewardAmount": {"style": "form"}, "QuestionsJson": {"style": "form"}, "EnableCompression": {"style": "form"}, "CompressionQuality": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoVideoCompleteUploadResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoVideoCompleteUploadResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoVideoCompleteUploadResponseDto"}}}}}}}, "/api/Video/update": {"post": {"tags": ["Video"], "summary": "更新视频信息", "operationId": "Video_Update", "requestBody": {"description": "更新视频DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoVideoUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoVideoUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoVideoUpdateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/Video/Delete/{videoId}": {"delete": {"tags": ["Video"], "summary": "删除视频", "parameters": [{"name": "videoId", "in": "path", "description": "视频ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/Video/Get/{videoId}": {"get": {"tags": ["Video"], "summary": "获取视频详情", "parameters": [{"name": "videoId", "in": "path", "description": "视频ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoVideoResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoVideoResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoVideoResponseDto"}}}}}}}, "/api/Video/creator/{createdBy}": {"get": {"tags": ["Video"], "summary": "获取创建者的视频列表", "operationId": "Video_GetCreatorVideos", "parameters": [{"name": "created<PERSON>y", "in": "path", "description": "创建者ID", "required": true, "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "状态筛选", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoVideoResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoVideoResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoVideoResponseDto"}}}}}}}, "/api/Video/my-videos": {"get": {"tags": ["Video"], "summary": "获取我的视频列表", "operationId": "Video_GetMyVideos", "parameters": [{"name": "status", "in": "query", "description": "状态筛选", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoVideoResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoVideoResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoVideoResponseDto"}}}}}}}, "/api/Video/{videoId}/status": {"post": {"tags": ["Video"], "summary": "更新视频状态", "operationId": "Video_UpdateVideoStatus", "parameters": [{"name": "videoId", "in": "path", "description": "视频ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "状态更新DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoVideoStatusUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoVideoStatusUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoVideoStatusUpdateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/Video/search": {"get": {"tags": ["Video"], "summary": "搜索视频", "operationId": "Video_SearchVideos", "parameters": [{"name": "keyword", "in": "query", "description": "搜索关键词", "schema": {"type": "string"}}, {"name": "pageIndex", "in": "query", "description": "页码", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "description": "页大小", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoVideoResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoVideoResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoVideoResponseDto"}}}}}}}, "/api/Video/{videoId}/statistics": {"get": {"tags": ["Video"], "summary": "获取视频统计信息", "operationId": "Video_GetVideoStatistics", "parameters": [{"name": "videoId", "in": "path", "description": "视频ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoVideoStatisticsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoVideoStatisticsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoVideoStatisticsDto"}}}}}}}, "/api/Video/compression-progress/{fileId}": {"get": {"tags": ["Video"], "summary": "获取视频压缩进度", "operationId": "Video_GetCompressionProgress", "parameters": [{"name": "fileId", "in": "path", "description": "文件ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfObject"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfObject"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfObject"}}}}}}}, "/api/Video/fix-compressed-videos": {"post": {"tags": ["Video"], "summary": "修复已压缩但数据库未更新的视频记录（临时接口）", "operationId": "Video_FixCompressedVideos", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfString"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfString"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfString"}}}}}}}, "/api/WechatAccessToken": {"post": {"tags": ["WechatAccessToken"], "summary": "创建微信访问令牌记录", "operationId": "WechatAccessToken_Create", "requestBody": {"description": "创建微信访问令牌DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoWechatAccessTokenCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoWechatAccessTokenCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoWechatAccessTokenCreateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}}}}}, "get": {"tags": ["WechatAccessToken"], "summary": "分页查询微信访问令牌列表", "parameters": [{"name": "AppId", "in": "query", "schema": {"type": "string"}}, {"name": "TokenType", "in": "query", "schema": {"type": "string"}}, {"name": "Is<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsExpired", "in": "query", "schema": {"type": "boolean"}}, {"name": "ObtainStartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ObtainEndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ExpiresStartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ExpiresEndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderField", "in": "query", "schema": {"type": "string"}}, {"name": "IsAsc", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoWechatAccessTokenResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoWechatAccessTokenResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoWechatAccessTokenResponseDto"}}}}}}}, "/api/WechatAccessToken/update": {"post": {"tags": ["WechatAccessToken"], "summary": "更新微信访问令牌记录", "operationId": "WechatAccessToken_Update", "requestBody": {"description": "更新微信访问令牌DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoWechatAccessTokenUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoWechatAccessTokenUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoWechatAccessTokenUpdateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/WechatAccessToken/{tokenId}/status": {"post": {"tags": ["WechatAccessToken"], "summary": "停用/激活微信访问令牌", "parameters": [{"name": "tokenId", "in": "path", "description": "令牌ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "是否激活", "content": {"application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}, "application/*+json": {"schema": {"type": "boolean"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/WechatAccessToken/{tokenId}": {"get": {"tags": ["WechatAccessToken"], "summary": "获取微信访问令牌详情", "parameters": [{"name": "tokenId", "in": "path", "description": "令牌ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWechatAccessTokenResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWechatAccessTokenResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWechatAccessTokenResponseDto"}}}}}}}, "/api/WechatAccessToken/valid": {"get": {"tags": ["WechatAccessToken"], "summary": "获取有效的访问令牌", "parameters": [{"name": "appId", "in": "query", "description": "微信应用ID", "schema": {"type": "string"}}, {"name": "tokenType", "in": "query", "description": "令牌类型", "schema": {"type": "string", "default": "access_token"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfString"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfString"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfString"}}}}}}}, "/api/WechatAccessToken/app/{appId}": {"get": {"tags": ["WechatAccessToken"], "summary": "获取应用的所有令牌", "parameters": [{"name": "appId", "in": "path", "description": "应用ID", "required": true, "schema": {"type": "string"}}, {"name": "isActive", "in": "query", "description": "是否激活（可选）", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoWechatAccessTokenResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoWechatAccessTokenResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoWechatAccessTokenResponseDto"}}}}}}}, "/api/WechatAccessToken/refresh": {"post": {"tags": ["WechatAccessToken"], "summary": "刷新访问令牌", "requestBody": {"description": "刷新令牌请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoTokenRefreshRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoTokenRefreshRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoTokenRefreshRequestDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/WechatAccessToken/batch-refresh": {"post": {"tags": ["WechatAccessToken"], "summary": "批量刷新访问令牌", "requestBody": {"description": "刷新请求列表", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VideoTokenRefreshRequestDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VideoTokenRefreshRequestDto"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/VideoTokenRefreshRequestDto"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchRefreshResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchRefreshResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoBatchRefreshResultDto"}}}}}}}, "/api/WechatAccessToken/statistics": {"get": {"tags": ["WechatAccessToken"], "summary": "获取令牌统计信息", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWechatTokenStatisticsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWechatTokenStatisticsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWechatTokenStatisticsDto"}}}}}}}, "/api/WechatAccessToken/expiring": {"get": {"tags": ["WechatAccessToken"], "summary": "获取即将过期的令牌列表", "parameters": [{"name": "bufferMinutes", "in": "query", "description": "缓冲时间（分钟）", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoWechatAccessTokenResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoWechatAccessTokenResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoWechatAccessTokenResponseDto"}}}}}}}, "/api/WechatAccessToken/check-expiring": {"get": {"tags": ["WechatAccessToken"], "summary": "检查令牌是否即将过期", "parameters": [{"name": "appId", "in": "query", "description": "应用ID", "schema": {"type": "string"}}, {"name": "tokenType", "in": "query", "description": "令牌类型", "schema": {"type": "string", "default": "access_token"}}, {"name": "bufferMinutes", "in": "query", "description": "缓冲时间（分钟）", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/WechatAccessToken/cleanup-expired": {"delete": {"tags": ["WechatAccessToken"], "summary": "清理过期令牌", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}}}}}}, "/api/WechatPayment/send-reward": {"post": {"tags": ["WechatPayment"], "summary": "发放红包\r\n用户完成视频观看和答题后，系统自动调用此接口发放红包", "operationId": "WechatPayment_SendReward", "requestBody": {"description": "红包发放DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoWechatPaymentCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoWechatPaymentCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoWechatPaymentCreateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}}}}}}, "/api/WechatPayment/{paymentId}": {"get": {"tags": ["WechatPayment"], "summary": "获取红包发放记录详情", "operationId": "WechatPayment_GetRewardRecord", "parameters": [{"name": "paymentId", "in": "path", "description": "支付记录ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWechatPaymentResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWechatPaymentResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWechatPaymentResponseDto"}}}}}}}, "/api/WechatPayment/trade/{outTradeNo}": {"get": {"tags": ["WechatPayment"], "summary": "根据商户订单号获取红包发放记录", "operationId": "WechatPayment_GetRewardByTradeNo", "parameters": [{"name": "outTradeNo", "in": "path", "description": "商户订单号", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWechatPaymentResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWechatPaymentResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWechatPaymentResponseDto"}}}}}}}, "/api/WechatPayment": {"get": {"tags": ["WechatPayment"], "summary": "分页查询红包发放记录列表", "parameters": [{"name": "RewardId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "OpenId", "in": "query", "schema": {"type": "string"}}, {"name": "OutTradeNo", "in": "query", "schema": {"type": "string"}}, {"name": "TransactionId", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PayStartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PayEndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "MinAmount", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "MaxAmount", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderNo", "in": "query", "schema": {"type": "string"}}, {"name": "PaymentType", "in": "query", "schema": {"type": "string"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderField", "in": "query", "schema": {"type": "string"}}, {"name": "IsAsc", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoWechatPaymentResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoWechatPaymentResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoWechatPaymentResponseDto"}}}}}}}, "/api/WechatPayment/user/{userId}": {"get": {"tags": ["WechatPayment"], "summary": "获取用户红包记录", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "发放状态（可选）", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoWechatPaymentResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoWechatPaymentResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoWechatPaymentResponseDto"}}}}}}}, "/api/WechatPayment/my-rewards": {"get": {"tags": ["WechatPayment"], "summary": "获取我的红包记录", "parameters": [{"name": "status", "in": "query", "description": "发放状态（可选）", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoWechatPaymentResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoWechatPaymentResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoWechatPaymentResponseDto"}}}}}}}, "/api/WechatPayment/callback": {"post": {"tags": ["WechatPayment"], "summary": "微信红包发放回调\r\n微信服务器调用此接口通知红包发放结果", "requestBody": {"description": "回调数据", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoWechatPaymentCallbackDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoWechatPaymentCallbackDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoWechatPaymentCallbackDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfString"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfString"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfString"}}}}}}}, "/api/WechatPayment/{paymentId}/status": {"get": {"tags": ["WechatPayment"], "summary": "查询红包发放状态", "parameters": [{"name": "paymentId", "in": "path", "description": "支付记录ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWechatPaymentResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWechatPaymentResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWechatPaymentResponseDto"}}}}}}, "post": {"tags": ["WechatPayment"], "summary": "更新红包发放状态\r\n管理员手动更新红包发放状态（如发放失败需要重新发放）", "parameters": [{"name": "paymentId", "in": "path", "description": "支付记录ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "status", "in": "query", "description": "新状态", "schema": {"type": "integer", "format": "int32"}}, {"name": "transactionId", "in": "query", "description": "微信交易号（可选）", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/WechatPayment/statistics": {"post": {"tags": ["WechatPayment"], "summary": "获取红包发放统计", "requestBody": {"description": "统计查询DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoWechatPaymentQueryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoWechatPaymentQueryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoWechatPaymentQueryDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWechatPaymentStatisticsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWechatPaymentStatisticsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfVideoWechatPaymentStatisticsDto"}}}}}}}, "/api/WechatPayment/export": {"post": {"tags": ["WechatPayment"], "summary": "导出红包发放记录", "requestBody": {"description": "导出查询DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoWechatPaymentQueryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VideoWechatPaymentQueryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VideoWechatPaymentQueryDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoWechatPaymentResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoWechatPaymentResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfVideoWechatPaymentResponseDto"}}}}}}}, "/api/WechatPayment/{paymentId}/retry": {"post": {"tags": ["WechatPayment"], "summary": "重新发放失败的红包\r\n对于发放失败的红包，管理员可以手动重新发放", "parameters": [{"name": "paymentId", "in": "path", "description": "支付记录ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfBoolean"}}}}}}}, "/api/WechatPayment/failed": {"get": {"tags": ["WechatPayment"], "summary": "获取红包发放失败记录", "parameters": [{"name": "RewardId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "OpenId", "in": "query", "schema": {"type": "string"}}, {"name": "OutTradeNo", "in": "query", "schema": {"type": "string"}}, {"name": "TransactionId", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PayStartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PayEndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "MinAmount", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "MaxAmount", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderNo", "in": "query", "schema": {"type": "string"}}, {"name": "PaymentType", "in": "query", "schema": {"type": "string"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderField", "in": "query", "schema": {"type": "string"}}, {"name": "IsAsc", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoWechatPaymentResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoWechatPaymentResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoWechatPaymentResponseDto"}}}}}}}, "/api/WechatPayment/pending": {"get": {"tags": ["WechatPayment"], "summary": "获取待发放红包记录", "parameters": [{"name": "RewardId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "OpenId", "in": "query", "schema": {"type": "string"}}, {"name": "OutTradeNo", "in": "query", "schema": {"type": "string"}}, {"name": "TransactionId", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PayStartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PayEndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "MinAmount", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "MaxAmount", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderNo", "in": "query", "schema": {"type": "string"}}, {"name": "PaymentType", "in": "query", "schema": {"type": "string"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderField", "in": "query", "schema": {"type": "string"}}, {"name": "IsAsc", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoWechatPaymentResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoWechatPaymentResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfPagedResultOfVideoWechatPaymentResponseDto"}}}}}}}, "/api/WechatPayment/batch-retry": {"post": {"tags": ["WechatPayment"], "summary": "批量重新发放失败的红包", "requestBody": {"description": "支付记录ID列表", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultOfInt32"}}}}}}}}, "components": {"schemas": {"ExportColumnDto": {"type": "object", "properties": {"title": {"type": "string", "nullable": true}, "propertyName": {"type": "string", "nullable": true}, "order": {"type": "integer", "format": "int32"}, "format": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ExportRequestDto": {"type": "object", "properties": {"columns": {"type": "array", "items": {"$ref": "#/components/schemas/ExportColumnDto"}, "nullable": true}, "fileName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "KeyMetricsSummaryDto": {"type": "object", "properties": {"totalMembers": {"type": "integer", "format": "int32"}, "todayNewMembers": {"type": "integer", "format": "int32"}, "totalOrders": {"type": "integer", "format": "int32"}, "viewerCount": {"type": "integer", "format": "int32"}, "completeRate": {"type": "number", "format": "double"}, "answerUserCount": {"type": "integer", "format": "int32"}, "correctRate": {"type": "number", "format": "double"}, "totalRewardAmount": {"type": "number", "format": "double"}, "untaggedUserCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "LogClearDto": {"required": ["beforeDate"], "type": "object", "properties": {"beforeDate": {"type": "string", "format": "date-time"}, "logType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PageEntityOfSysLog": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPrevPages": {"type": "boolean", "readOnly": true}, "hasNextPages": {"type": "boolean", "readOnly": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/SysLog"}, "nullable": true}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/SysLog"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "PagedResultOfVideoBatchResponseDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/VideoBatchResponseDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "PagedResultOfVideoSystemConfigResponseDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/VideoSystemConfigResponseDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "PagedResultOfVideoUserBatchRecordResponseDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/VideoUserBatchRecordResponseDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "PagedResultOfVideoUserDailyStatisticsDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/VideoUserDailyStatisticsDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "PagedResultOfVideoUserResponseDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/VideoUserResponseDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "PagedResultOfVideoUserTransferResponseDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/VideoUserTransferResponseDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "PagedResultOfVideoVideoResponseDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/VideoVideoResponseDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "PagedResultOfVideoWechatAccessTokenResponseDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/VideoWechatAccessTokenResponseDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "PagedResultOfVideoWechatPaymentResponseDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/VideoWechatPaymentResponseDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Result": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}}, "additionalProperties": false, "description": "接口返回规范"}, "ResultOfBoolean": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "boolean", "description": "接口返回数据"}}, "additionalProperties": false}, "ResultOfDictionaryOfStringString": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "object", "additionalProperties": {"type": "string", "nullable": true}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ResultOfInt32": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "integer", "description": "接口返回数据", "format": "int32"}}, "additionalProperties": false}, "ResultOfKeyMetricsSummaryDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/KeyMetricsSummaryDto"}}, "additionalProperties": false}, "ResultOfListOfString": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"type": "string"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ResultOfListOfSysUserWithStatisticsDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SysUserWithStatisticsDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ResultOfListOfVideoDailyStatisticsTrendDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/VideoDailyStatisticsTrendDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ResultOfListOfVideoStatisticsResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/VideoStatisticsResponseDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ResultOfListOfVideoSystemConfigResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/VideoSystemConfigResponseDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ResultOfListOfVideoTagStatisticsDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/VideoTagStatisticsDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ResultOfListOfVideoUserBatchRecordResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/VideoUserBatchRecordResponseDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ResultOfListOfVideoUserBatchRecordSummaryDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/VideoUserBatchRecordSummaryDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ResultOfListOfVideoUserResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/VideoUserResponseDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ResultOfListOfVideoUserStatisticsSummaryDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/VideoUserStatisticsSummaryDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ResultOfListOfVideoVideoResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/VideoVideoResponseDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ResultOfListOfVideoWechatAccessTokenResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/VideoWechatAccessTokenResponseDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ResultOfListOfVideoWechatPaymentResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/VideoWechatPaymentResponseDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ResultOfObject": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ResultOfPageEntityOfSysLog": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/PageEntityOfSysLog"}}, "additionalProperties": false}, "ResultOfPagedResultOfVideoBatchResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/PagedResultOfVideoBatchResponseDto"}}, "additionalProperties": false}, "ResultOfPagedResultOfVideoSystemConfigResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/PagedResultOfVideoSystemConfigResponseDto"}}, "additionalProperties": false}, "ResultOfPagedResultOfVideoUserBatchRecordResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/PagedResultOfVideoUserBatchRecordResponseDto"}}, "additionalProperties": false}, "ResultOfPagedResultOfVideoUserDailyStatisticsDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/PagedResultOfVideoUserDailyStatisticsDto"}}, "additionalProperties": false}, "ResultOfPagedResultOfVideoUserResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/PagedResultOfVideoUserResponseDto"}}, "additionalProperties": false}, "ResultOfPagedResultOfVideoUserTransferResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/PagedResultOfVideoUserTransferResponseDto"}}, "additionalProperties": false}, "ResultOfPagedResultOfVideoVideoResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/PagedResultOfVideoVideoResponseDto"}}, "additionalProperties": false}, "ResultOfPagedResultOfVideoWechatAccessTokenResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/PagedResultOfVideoWechatAccessTokenResponseDto"}}, "additionalProperties": false}, "ResultOfPagedResultOfVideoWechatPaymentResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/PagedResultOfVideoWechatPaymentResponseDto"}}, "additionalProperties": false}, "ResultOfString": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "string", "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ResultOfSysLog": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/SysLog"}}, "additionalProperties": false}, "ResultOfSysLoginResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/SysLoginResponseDto"}}, "additionalProperties": false}, "ResultOfSysUserDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/SysUserDto"}}, "additionalProperties": false}, "ResultOfSysUserInfoDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/SysUserInfoDto"}}, "additionalProperties": false}, "ResultOfVideoAccessPromotionResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoAccessPromotionResponseDto"}}, "additionalProperties": false}, "ResultOfVideoAnswerDetailDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoAnswerDetailDto"}}, "additionalProperties": false}, "ResultOfVideoAnswerEligibilityDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoAnswerEligibilityDto"}}, "additionalProperties": false}, "ResultOfVideoAnswerStatisticsDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoAnswerStatisticsDto"}}, "additionalProperties": false}, "ResultOfVideoAnswerStatusDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoAnswerStatusDto"}}, "additionalProperties": false}, "ResultOfVideoBatchRefreshResultDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoBatchRefreshResultDto"}}, "additionalProperties": false}, "ResultOfVideoBatchResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoBatchResponseDto"}}, "additionalProperties": false}, "ResultOfVideoBatchRewardResultDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoBatchRewardResultDto"}}, "additionalProperties": false}, "ResultOfVideoBatchStatisticsDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoBatchStatisticsDto"}}, "additionalProperties": false}, "ResultOfVideoBatchStatisticsFromRecordDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoBatchStatisticsFromRecordDto"}}, "additionalProperties": false}, "ResultOfVideoCourseStatisticsDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoCourseStatisticsDto"}}, "additionalProperties": false}, "ResultOfVideoDashboardDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoDashboardDto"}}, "additionalProperties": false}, "ResultOfVideoDashboardSummaryDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoDashboardSummaryDto"}}, "additionalProperties": false}, "ResultOfVideoLoginResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoLoginResponseDto"}}, "additionalProperties": false}, "ResultOfVideoOrderStatisticsDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoOrderStatisticsDto"}}, "additionalProperties": false}, "ResultOfVideoRewardEligibilityDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoRewardEligibilityDto"}}, "additionalProperties": false}, "ResultOfVideoRewardStatisticsDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoRewardStatisticsDto"}}, "additionalProperties": false}, "ResultOfVideoRewardStatusDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoRewardStatusDto"}}, "additionalProperties": false}, "ResultOfVideoStatisticsOverviewDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoStatisticsOverviewDto"}}, "additionalProperties": false}, "ResultOfVideoStatisticsSummaryDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoStatisticsSummaryDto"}}, "additionalProperties": false}, "ResultOfVideoSystemConfigResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoSystemConfigResponseDto"}}, "additionalProperties": false}, "ResultOfVideoUserBatchRecordResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoUserBatchRecordResponseDto"}}, "additionalProperties": false}, "ResultOfVideoUserResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoUserResponseDto"}}, "additionalProperties": false}, "ResultOfVideoUserStatisticsSummaryDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoUserStatisticsSummaryDto"}}, "additionalProperties": false}, "ResultOfVideoVideoCompleteUploadResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoVideoCompleteUploadResponseDto"}}, "additionalProperties": false}, "ResultOfVideoVideoResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoVideoResponseDto"}}, "additionalProperties": false}, "ResultOfVideoVideoStatisticsDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoVideoStatisticsDto"}}, "additionalProperties": false}, "ResultOfVideoVideoUploadResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoVideoUploadResponseDto"}}, "additionalProperties": false}, "ResultOfVideoWatchStatusDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoWatchStatusDto"}}, "additionalProperties": false}, "ResultOfVideoWechatAccessTokenResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoWechatAccessTokenResponseDto"}}, "additionalProperties": false}, "ResultOfVideoWechatPaymentResponseDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoWechatPaymentResponseDto"}}, "additionalProperties": false}, "ResultOfVideoWechatPaymentStatisticsDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoWechatPaymentStatisticsDto"}}, "additionalProperties": false}, "ResultOfVideoWechatTokenStatisticsDto": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/VideoWechatTokenStatisticsDto"}}, "additionalProperties": false}, "SysChangePasswordDto": {"required": ["newPassword", "oldPassword", "userId"], "type": "object", "properties": {"userId": {"minLength": 1, "type": "string"}, "oldPassword": {"minLength": 1, "type": "string"}, "newPassword": {"minLength": 6, "type": "string"}}, "additionalProperties": false}, "SysCreateUserDto": {"required": ["password", "userName", "userType"], "type": "object", "properties": {"realName": {"maxLength": 50, "type": "string", "nullable": true}, "avatar": {"maxLength": 200, "type": "string", "nullable": true}, "email": {"maxLength": 100, "type": "string", "nullable": true}, "mobile": {"maxLength": 20, "type": "string", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "userName": {"maxLength": 50, "minLength": 1, "type": "string"}, "password": {"maxLength": 100, "minLength": 1, "type": "string"}, "userType": {"maximum": 3, "minimum": 2, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "SysLog": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "userId": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}, "operation": {"type": "string", "nullable": true}, "method": {"type": "string", "nullable": true}, "params": {"type": "string", "nullable": true}, "time": {"type": "integer", "format": "int64"}, "ip": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "logType": {"type": "string", "nullable": true}, "logLevel": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "exception": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "SysLoginRequestDto": {"required": ["password", "username"], "type": "object", "properties": {"username": {"minLength": 1, "type": "string"}, "password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "SysLoginResponseDto": {"type": "object", "properties": {"accessToken": {"type": "string", "nullable": true}, "userInfo": {"$ref": "#/components/schemas/SysUserInfoDto"}}, "additionalProperties": false}, "SysResetPasswordDto": {"required": ["newPassword", "userId"], "type": "object", "properties": {"userId": {"minLength": 1, "type": "string"}, "newPassword": {"minLength": 6, "type": "string"}}, "additionalProperties": false}, "SysUpdateUserDto": {"required": ["userId"], "type": "object", "properties": {"realName": {"maxLength": 50, "type": "string", "nullable": true}, "avatar": {"maxLength": 200, "type": "string", "nullable": true}, "email": {"maxLength": 100, "type": "string", "nullable": true}, "mobile": {"maxLength": 20, "type": "string", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "userId": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "SysUserDto": {"type": "object", "properties": {"realName": {"maxLength": 50, "type": "string", "nullable": true}, "avatar": {"maxLength": 200, "type": "string", "nullable": true}, "email": {"maxLength": 100, "type": "string", "nullable": true}, "mobile": {"maxLength": 20, "type": "string", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "id": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "roleName": {"type": "string", "nullable": true}, "roleCode": {"type": "string", "nullable": true}, "lastLoginTime": {"type": "string", "format": "date-time", "nullable": true}, "lastLoginIp": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "SysUserInfoDto": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}, "nickName": {"type": "string", "nullable": true}, "userType": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "SysUserStatisticsSummaryDto": {"type": "object", "properties": {"totalViewCount": {"type": "integer", "format": "int32"}, "totalAnswerCount": {"type": "integer", "format": "int32"}, "totalRewardAmount": {"type": "number", "format": "double"}, "completeViewCount": {"type": "integer", "format": "int32"}, "correctAnswerCount": {"type": "integer", "format": "int32"}, "rewardClaimCount": {"type": "integer", "format": "int32"}, "completeRate": {"type": "number", "format": "double"}, "correctRate": {"type": "number", "format": "double"}, "statisticsStartTime": {"type": "string", "format": "date-time", "nullable": true}, "statisticsEndTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "SysUserWithStatisticsDto": {"type": "object", "properties": {"realName": {"maxLength": 50, "type": "string", "nullable": true}, "avatar": {"maxLength": 200, "type": "string", "nullable": true}, "email": {"maxLength": 100, "type": "string", "nullable": true}, "mobile": {"maxLength": 20, "type": "string", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "id": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "roleName": {"type": "string", "nullable": true}, "roleCode": {"type": "string", "nullable": true}, "lastLoginTime": {"type": "string", "format": "date-time", "nullable": true}, "lastLoginIp": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "userType": {"type": "integer", "format": "int32"}, "parentUserId": {"type": "string", "nullable": true}, "parentUserName": {"type": "string", "nullable": true}, "directSubordinateCount": {"type": "integer", "format": "int32"}, "totalSubordinateUserCount": {"type": "integer", "format": "int32"}, "statistics": {"$ref": "#/components/schemas/SysUserStatisticsSummaryDto"}, "userTypeName": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "VideoAccessPromotionDto": {"required": ["batchId", "userId"], "type": "object", "properties": {"batchId": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "promotionLink": {"type": "string", "nullable": true}, "source": {"type": "string", "nullable": true}, "employeeId": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VideoAccessPromotionResponseDto": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "recordId": {"type": "integer", "format": "int32", "nullable": true}, "isNewRecord": {"type": "boolean"}, "needAudit": {"type": "boolean"}, "auditStatus": {"type": "string", "nullable": true}, "batch": {"nullable": true}, "user": {"nullable": true}}, "additionalProperties": false}, "VideoAnswerDetailDto": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true}, "userNickname": {"type": "string", "nullable": true}, "batchId": {"type": "integer", "format": "int32"}, "batchName": {"type": "string", "nullable": true}, "totalQuestions": {"type": "integer", "format": "int32"}, "correctAnswers": {"type": "integer", "format": "int32"}, "correctRate": {"type": "number", "format": "double"}, "answerDetails": {"type": "string", "nullable": true}, "answerTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "VideoAnswerEligibilityDto": {"type": "object", "properties": {"canAnswer": {"type": "boolean"}, "reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VideoAnswerStatisticsCompareDto": {"type": "object", "properties": {"answerUserCountChange": {"type": "integer", "format": "int32"}, "correctUserCountChange": {"type": "integer", "format": "int32"}, "correctRateChange": {"type": "number", "format": "double"}, "answerUserCountChangeText": {"type": "string", "nullable": true}, "correctUserCountChangeText": {"type": "string", "nullable": true}, "correctRateChangeText": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VideoAnswerStatisticsDto": {"type": "object", "properties": {"answerUserCount": {"type": "integer", "format": "int32"}, "correctUserCount": {"type": "integer", "format": "int32"}, "correctRate": {"type": "number", "format": "double"}, "totalAnswerCount": {"type": "integer", "format": "int32"}, "correctAnswerCount": {"type": "integer", "format": "int32"}, "averageAnswerTime": {"type": "integer", "format": "int32"}, "compare": {"$ref": "#/components/schemas/VideoAnswerStatisticsCompareDto"}}, "additionalProperties": false}, "VideoAnswerStatusDto": {"type": "object", "properties": {"hasRecord": {"type": "boolean"}, "hasAnswered": {"type": "boolean"}, "canAnswer": {"type": "boolean"}, "totalQuestions": {"type": "integer", "format": "int32"}, "correctAnswers": {"type": "integer", "format": "int32"}, "correctRate": {"type": "number", "format": "double"}, "answerTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "VideoAnswerSubmitDto": {"required": ["answerDetails", "batchId"], "type": "object", "properties": {"batchId": {"type": "integer", "format": "int32"}, "totalQuestions": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}, "correctAnswers": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "answerDetails": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "VideoBatchCreateDto": {"required": ["endTime", "name", "startTime", "videoId"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 1, "type": "string"}, "description": {"maxLength": 255, "type": "string", "nullable": true}, "videoId": {"type": "integer", "format": "int32"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "redPacketAmount": {"type": "number", "format": "double"}}, "additionalProperties": false}, "VideoBatchRefreshResultDto": {"type": "object", "properties": {"successTokenTypes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "failedTokenTypes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "failureReasons": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "successCount": {"type": "integer", "format": "int32"}, "failedCount": {"type": "integer", "format": "int32"}, "failureCount": {"type": "integer", "format": "int32"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/VideoTokenRefreshResultItemDto"}, "nullable": true}, "refreshTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "VideoBatchResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "videoId": {"type": "integer", "format": "int32"}, "videoTitle": {"type": "string", "nullable": true}, "videoDescription": {"type": "string", "nullable": true}, "videoCoverUrl": {"type": "string", "nullable": true}, "videoUrl": {"type": "string", "nullable": true}, "videoDuration": {"type": "integer", "format": "int32"}, "rewardAmount": {"type": "number", "format": "double"}, "questions": {"type": "array", "items": {"$ref": "#/components/schemas/VideoVideoQuestionDto"}, "nullable": true}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "currentParticipants": {"type": "integer", "format": "int32"}, "redPacketAmount": {"type": "number", "format": "double"}, "createTime": {"type": "string", "format": "date-time"}, "creatorName": {"type": "string", "nullable": true}, "statistics": {"$ref": "#/components/schemas/VideoBatchStatisticsDto"}}, "additionalProperties": false}, "VideoBatchRewardResultDto": {"type": "object", "properties": {"batchId": {"type": "integer", "format": "int32"}, "totalEligible": {"type": "integer", "format": "int32"}, "successCount": {"type": "integer", "format": "int32"}, "failedCount": {"type": "integer", "format": "int32"}, "failedUsers": {"type": "array", "items": {"type": "string"}, "nullable": true}, "successRate": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "VideoBatchStatisticsDto": {"type": "object", "properties": {"viewCount": {"type": "integer", "format": "int32"}, "completeViewCount": {"type": "integer", "format": "int32"}, "completeRate": {"type": "number", "format": "double"}, "newUserCount": {"type": "integer", "format": "int32"}, "correctAnswerCount": {"type": "integer", "format": "int32"}, "totalAnswerCount": {"type": "integer", "format": "int32"}, "correctRate": {"type": "number", "format": "double"}, "rewardCount": {"type": "integer", "format": "int32"}, "rewardAmount": {"type": "number", "format": "double"}, "totalCount": {"type": "integer", "format": "int32"}, "activeCount": {"type": "integer", "format": "int32"}, "completedCount": {"type": "integer", "format": "int32"}, "totalParticipants": {"type": "integer", "format": "int32"}, "totalRedPacketAmount": {"type": "number", "format": "double"}}, "additionalProperties": false}, "VideoBatchStatisticsFromRecordDto": {"type": "object", "properties": {"batchId": {"type": "integer", "format": "int32"}, "batchName": {"type": "string", "nullable": true}, "totalParticipants": {"type": "integer", "format": "int32"}, "viewerCount": {"type": "integer", "format": "int32"}, "unwatchedUserCount": {"type": "integer", "format": "int32"}, "completedViewerCount": {"type": "integer", "format": "int32"}, "completeRate": {"type": "number", "format": "double"}, "answerCount": {"type": "integer", "format": "int32"}, "answerRate": {"type": "number", "format": "double"}, "averageCorrectRate": {"type": "number", "format": "double"}, "rewardCount": {"type": "integer", "format": "int32"}, "totalRewardAmount": {"type": "number", "format": "double"}, "successRewardCount": {"type": "integer", "format": "int32"}, "rewardSuccessRate": {"type": "number", "format": "double"}, "averageViewDuration": {"type": "integer", "format": "int32"}, "averageWatchProgress": {"type": "number", "format": "double"}}, "additionalProperties": false}, "VideoBatchWatchProgressUpdateDto": {"required": ["batchId", "userId"], "type": "object", "properties": {"userId": {"minLength": 1, "type": "string"}, "batchId": {"type": "integer", "format": "int32"}, "viewDuration": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "watchProgress": {"maximum": 1, "minimum": 0, "type": "number", "format": "double"}, "isCompleted": {"type": "boolean"}}, "additionalProperties": false}, "VideoCourseStatisticsCompareDto": {"type": "object", "properties": {"viewerCountChange": {"type": "integer", "format": "int32"}, "completeViewerCountChange": {"type": "integer", "format": "int32"}, "completeRateChange": {"type": "number", "format": "double"}, "viewerCountChangeText": {"type": "string", "nullable": true}, "completeViewerCountChangeText": {"type": "string", "nullable": true}, "completeRateChangeText": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VideoCourseStatisticsDto": {"type": "object", "properties": {"viewerCount": {"type": "integer", "format": "int32"}, "completeViewerCount": {"type": "integer", "format": "int32"}, "completeRate": {"type": "number", "format": "double"}, "totalViews": {"type": "integer", "format": "int32"}, "totalCompleteViews": {"type": "integer", "format": "int32"}, "averageViewDuration": {"type": "integer", "format": "int32"}, "compare": {"$ref": "#/components/schemas/VideoCourseStatisticsCompareDto"}}, "additionalProperties": false}, "VideoDailyStatisticsTrendDto": {"type": "object", "properties": {"statDate": {"type": "string", "format": "date-time"}, "viewCount": {"type": "integer", "format": "int32"}, "completeViewCount": {"type": "integer", "format": "int32"}, "answerCount": {"type": "integer", "format": "int32"}, "correctAnswerCount": {"type": "integer", "format": "int32"}, "rewardCount": {"type": "integer", "format": "int32"}, "rewardAmountYuan": {"type": "number", "format": "double"}, "activeUserCount": {"type": "integer", "format": "int32"}, "completeRate": {"type": "number", "format": "double", "readOnly": true}, "correctRate": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "VideoDashboardDto": {"type": "object", "properties": {"summary": {"$ref": "#/components/schemas/VideoDashboardSummaryDto"}, "tagStatistics": {"type": "array", "items": {"$ref": "#/components/schemas/VideoTagStatisticsDto"}, "nullable": true}, "courseStatistics": {"$ref": "#/components/schemas/VideoCourseStatisticsDto"}, "answerStatistics": {"$ref": "#/components/schemas/VideoAnswerStatisticsDto"}, "rewardStatistics": {"$ref": "#/components/schemas/VideoRewardStatisticsDto"}, "orderStatistics": {"$ref": "#/components/schemas/VideoOrderStatisticsDto"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "VideoDashboardSummaryDto": {"type": "object", "properties": {"totalMembers": {"type": "integer", "format": "int32"}, "todayNewMembers": {"type": "integer", "format": "int32"}, "totalOrders": {"type": "integer", "format": "int32"}, "todayOrders": {"type": "integer", "format": "int32"}, "totalOrderAmount": {"type": "number", "format": "double"}, "todayOrderAmount": {"type": "number", "format": "double"}}, "additionalProperties": false}, "VideoLoginResponseDto": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "userInfo": {"$ref": "#/components/schemas/VideoUserResponseDto"}}, "additionalProperties": false}, "VideoOrderStatisticsDto": {"type": "object", "properties": {"totalCount": {"type": "integer", "format": "int32"}, "successCount": {"type": "integer", "format": "int32"}, "failedCount": {"type": "integer", "format": "int32"}, "pendingCount": {"type": "integer", "format": "int32"}, "totalAmount": {"type": "number", "format": "double"}, "successAmount": {"type": "number", "format": "double"}, "averageAmount": {"type": "number", "format": "double"}, "successRate": {"type": "number", "format": "double"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "VideoRewardEligibilityDto": {"type": "object", "properties": {"canReceiveReward": {"type": "boolean"}, "reason": {"type": "string", "nullable": true}, "suggestedAmount": {"type": "number", "format": "double"}}, "additionalProperties": false}, "VideoRewardGrantDto": {"required": ["batchId"], "type": "object", "properties": {"batchId": {"type": "integer", "format": "int32"}, "rewardAmount": {"maximum": 200, "minimum": 0.01, "type": "number", "format": "double"}, "transactionId": {"maxLength": 100, "type": "string", "nullable": true}, "outTradeNo": {"maxLength": 100, "type": "string", "nullable": true}}, "additionalProperties": false}, "VideoRewardStatisticsCompareDto": {"type": "object", "properties": {"rewardCountChange": {"type": "integer", "format": "int32"}, "rewardAmountChange": {"type": "number", "format": "double"}, "rewardCountChangeText": {"type": "string", "nullable": true}, "rewardAmountChangeText": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VideoRewardStatisticsDto": {"type": "object", "properties": {"answerRewardCount": {"type": "integer", "format": "int32"}, "answerRewardAmount": {"type": "number", "format": "double"}, "viewRewardCount": {"type": "integer", "format": "int32"}, "viewRewardAmount": {"type": "number", "format": "double"}, "shareRewardCount": {"type": "integer", "format": "int32"}, "shareRewardAmount": {"type": "number", "format": "double"}, "totalRewardCount": {"type": "integer", "format": "int32"}, "totalRewardAmount": {"type": "number", "format": "double"}, "distributedCount": {"type": "integer", "format": "int32"}, "distributedAmount": {"type": "number", "format": "double"}, "distributionSuccessRate": {"type": "number", "format": "double"}, "compare": {"$ref": "#/components/schemas/VideoRewardStatisticsCompareDto"}}, "additionalProperties": false}, "VideoRewardStatusDto": {"type": "object", "properties": {"hasRecord": {"type": "boolean"}, "hasReward": {"type": "boolean"}, "canReceiveReward": {"type": "boolean"}, "rewardAmount": {"type": "number", "format": "double"}, "rewardStatus": {"type": "integer", "format": "int32"}, "rewardStatusText": {"type": "string", "nullable": true}, "rewardTime": {"type": "string", "format": "date-time", "nullable": true}, "rewardFailReason": {"type": "string", "nullable": true}, "rewardRetryCount": {"type": "integer", "format": "int32"}, "transactionId": {"type": "string", "nullable": true}, "outTradeNo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VideoRewardStatusUpdateDto": {"required": ["batchId"], "type": "object", "properties": {"batchId": {"type": "integer", "format": "int32"}, "rewardStatus": {"maximum": 2, "minimum": 0, "type": "integer", "format": "int32"}, "failReason": {"maxLength": 255, "type": "string", "nullable": true}, "transactionId": {"maxLength": 100, "type": "string", "nullable": true}, "outTradeNo": {"maxLength": 100, "type": "string", "nullable": true}}, "additionalProperties": false}, "VideoStatisticsOverviewDto": {"type": "object", "properties": {"totalUsers": {"type": "integer", "format": "int32"}, "totalViewCount": {"type": "integer", "format": "int32"}, "totalCompleteViewCount": {"type": "integer", "format": "int32"}, "totalAnswerCount": {"type": "integer", "format": "int32"}, "totalCorrectAnswerCount": {"type": "integer", "format": "int32"}, "totalRewardCount": {"type": "integer", "format": "int32"}, "totalRewardAmountYuan": {"type": "number", "format": "double"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "avgCompleteRate": {"type": "number", "format": "double", "readOnly": true}, "avgCorrectRate": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "VideoStatisticsResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "statDate": {"type": "string", "format": "date-time"}, "batchId": {"type": "integer", "format": "int32"}, "batchName": {"type": "string", "nullable": true}, "adminId": {"type": "integer", "format": "int32", "nullable": true}, "adminName": {"type": "string", "nullable": true}, "employeeId": {"type": "integer", "format": "int32", "nullable": true}, "employeeName": {"type": "string", "nullable": true}, "userId": {"type": "string", "nullable": true}, "videoId": {"type": "integer", "format": "int32", "nullable": true}, "statType": {"type": "string", "nullable": true}, "shareCount": {"type": "integer", "format": "int32"}, "likeCount": {"type": "integer", "format": "int32"}, "commentCount": {"type": "integer", "format": "int32"}, "downloadCount": {"type": "integer", "format": "int32"}, "duration": {"type": "integer", "format": "int64"}, "viewCount": {"type": "integer", "format": "int32"}, "completeViewCount": {"type": "integer", "format": "int32"}, "completeRate": {"type": "number", "format": "double"}, "newUserCount": {"type": "integer", "format": "int32"}, "correctAnswerCount": {"type": "integer", "format": "int32"}, "totalAnswerCount": {"type": "integer", "format": "int32"}, "correctRate": {"type": "number", "format": "double"}, "rewardCount": {"type": "integer", "format": "int32"}, "rewardAmount": {"type": "integer", "format": "int32"}, "rewardAmountYuan": {"type": "number", "format": "double", "readOnly": true}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "VideoStatisticsSummaryDto": {"type": "object", "properties": {"totalViewCount": {"type": "integer", "format": "int32"}, "totalCompleteViewCount": {"type": "integer", "format": "int32"}, "averageCompleteRate": {"type": "number", "format": "double"}, "totalNewUserCount": {"type": "integer", "format": "int32"}, "totalCorrectAnswerCount": {"type": "integer", "format": "int32"}, "totalAnswerCount": {"type": "integer", "format": "int32"}, "averageCorrectRate": {"type": "number", "format": "double"}, "totalRewardCount": {"type": "integer", "format": "int32"}, "totalRewardAmount": {"type": "integer", "format": "int32"}, "totalRewardAmountYuan": {"type": "number", "format": "double", "readOnly": true}, "totalShareCount": {"type": "integer", "format": "int32"}, "totalLikeCount": {"type": "integer", "format": "int32"}, "totalCommentCount": {"type": "integer", "format": "int32"}, "totalDownloadCount": {"type": "integer", "format": "int32"}, "totalDuration": {"type": "integer", "format": "int32"}, "averageViewCount": {"type": "number", "format": "double"}, "averageShareCount": {"type": "number", "format": "double"}, "averageLikeCount": {"type": "number", "format": "double"}, "averageCommentCount": {"type": "number", "format": "double"}, "averageDownloadCount": {"type": "number", "format": "double"}, "averageDuration": {"type": "number", "format": "double"}, "statDays": {"type": "integer", "format": "int32"}, "startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "VideoStatisticsSummaryQueryDto": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int32", "nullable": true}, "videoId": {"type": "integer", "format": "int32", "nullable": true}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "statType": {"type": "string", "nullable": true}, "batchIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "adminId": {"type": "integer", "format": "int32", "nullable": true}, "employeeId": {"type": "integer", "format": "int32", "nullable": true}, "startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "VideoSystemConfigCreateDto": {"required": ["config<PERSON><PERSON>"], "type": "object", "properties": {"configKey": {"maxLength": 100, "minLength": 1, "type": "string"}, "configValue": {"type": "string", "nullable": true}, "description": {"maxLength": 255, "type": "string", "nullable": true}, "groupName": {"maxLength": 50, "type": "string", "nullable": true}, "configType": {"maxLength": 50, "type": "string", "nullable": true}, "isEnabled": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "VideoSystemConfigResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "configKey": {"type": "string", "nullable": true}, "configValue": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "groupName": {"type": "string", "nullable": true}, "configType": {"type": "string", "nullable": true}, "isEnabled": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "VideoSystemConfigUpdateDto": {"required": ["config<PERSON><PERSON>", "id"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "configKey": {"maxLength": 100, "minLength": 1, "type": "string"}, "configValue": {"type": "string", "nullable": true}, "configType": {"maxLength": 50, "type": "string", "nullable": true}, "description": {"maxLength": 255, "type": "string", "nullable": true}, "isEnabled": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "VideoTagStatisticsDto": {"type": "object", "properties": {"tagId": {"type": "integer", "format": "int32", "nullable": true}, "tagName": {"type": "string", "nullable": true}, "userCount": {"type": "integer", "format": "int32"}, "isUntagged": {"type": "boolean"}, "percentage": {"type": "number", "format": "double"}}, "additionalProperties": false}, "VideoTokenRefreshRequestDto": {"required": ["tokenTypes"], "type": "object", "properties": {"tokenTypes": {"type": "array", "items": {"type": "string"}}, "appId": {"type": "string", "nullable": true}, "tokenType": {"type": "string", "nullable": true}, "newAccessToken": {"type": "string", "nullable": true}, "expiresIn": {"type": "integer", "format": "int32"}, "forceRefresh": {"type": "boolean"}}, "additionalProperties": false}, "VideoTokenRefreshResultItemDto": {"type": "object", "properties": {"appId": {"type": "string", "nullable": true}, "tokenType": {"type": "string", "nullable": true}, "isSuccess": {"type": "boolean"}, "errorMessage": {"type": "string", "nullable": true}, "newToken": {"type": "string", "nullable": true}, "expiresAt": {"type": "string", "format": "date-time", "nullable": true}, "success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VideoUserAuditDto": {"required": ["status"], "type": "object", "properties": {"status": {"maximum": 2, "minimum": 1, "type": "integer", "format": "int32"}, "remark": {"maxLength": 255, "type": "string", "nullable": true}}, "additionalProperties": false}, "VideoUserBatchRecordCreateDto": {"required": ["batchId"], "type": "object", "properties": {"batchId": {"type": "integer", "format": "int32"}, "userId": {"type": "string", "nullable": true}, "promotionLink": {"maxLength": 500, "type": "string", "nullable": true}}, "additionalProperties": false}, "VideoUserBatchRecordResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "userId": {"type": "string", "nullable": true}, "userNickname": {"type": "string", "nullable": true}, "userAvatar": {"type": "string", "nullable": true}, "employeeId": {"type": "string", "nullable": true}, "employeeName": {"type": "string", "nullable": true}, "batchId": {"type": "integer", "format": "int32"}, "batchName": {"type": "string", "nullable": true}, "videoTitle": {"type": "string", "nullable": true}, "viewDuration": {"type": "integer", "format": "int32"}, "watchProgress": {"type": "number", "format": "double"}, "watchProgressPercent": {"type": "number", "format": "double"}, "isCompleted": {"type": "boolean"}, "startTime": {"type": "string", "format": "date-time", "nullable": true}, "lastWatchTime": {"type": "string", "format": "date-time", "nullable": true}, "totalQuestions": {"type": "integer", "format": "int32"}, "correctAnswers": {"type": "integer", "format": "int32"}, "correctRate": {"type": "number", "format": "double"}, "hasAnswered": {"type": "boolean"}, "answerTime": {"type": "string", "format": "date-time", "nullable": true}, "rewardAmount": {"type": "number", "format": "double"}, "rewardStatus": {"type": "integer", "format": "int32"}, "rewardStatusText": {"type": "string", "nullable": true}, "hasReward": {"type": "boolean"}, "rewardTime": {"type": "string", "format": "date-time", "nullable": true}, "rewardFailReason": {"type": "string", "nullable": true}, "promotionLink": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "VideoUserBatchRecordSummaryDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "userId": {"type": "string", "nullable": true}, "userNickname": {"type": "string", "nullable": true}, "batchId": {"type": "integer", "format": "int32"}, "batchName": {"type": "string", "nullable": true}, "videoCoverUrl": {"type": "string", "nullable": true}, "watchProgressPercent": {"type": "number", "format": "double"}, "isCompleted": {"type": "boolean"}, "hasAnswered": {"type": "boolean"}, "correctRate": {"type": "number", "format": "double"}, "totalQuestions": {"type": "integer", "format": "int32"}, "correctAnswers": {"type": "integer", "format": "int32"}, "rewardAmount": {"type": "number", "format": "double"}, "rewardStatus": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "VideoUserDailyStatisticsDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "userId": {"type": "string", "nullable": true}, "statDate": {"type": "string", "format": "date-time"}, "employeeId": {"type": "integer", "format": "int32", "nullable": true}, "employeeName": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "viewCount": {"type": "integer", "format": "int32"}, "completeViewCount": {"type": "integer", "format": "int32"}, "totalViewDuration": {"type": "integer", "format": "int32"}, "answerCount": {"type": "integer", "format": "int32"}, "correctAnswerCount": {"type": "integer", "format": "int32"}, "rewardCount": {"type": "integer", "format": "int32"}, "rewardAmount": {"type": "integer", "format": "int64"}, "shareCount": {"type": "integer", "format": "int32"}, "likeCount": {"type": "integer", "format": "int32"}, "commentCount": {"type": "integer", "format": "int32"}, "downloadCount": {"type": "integer", "format": "int32"}, "completeRate": {"type": "number", "format": "double", "readOnly": true}, "correctRate": {"type": "number", "format": "double", "readOnly": true}, "avgViewDuration": {"type": "number", "format": "double", "readOnly": true}, "rewardAmountYuan": {"type": "number", "format": "double", "readOnly": true}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "VideoUserResponseDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "openId": {"type": "string", "nullable": true}, "unionId": {"type": "string", "nullable": true}, "nickname": {"type": "string", "nullable": true}, "avatar": {"type": "string", "nullable": true}, "employeeId": {"type": "string", "nullable": true}, "employeeName": {"type": "string", "nullable": true}, "lastLogin": {"type": "string", "format": "date-time", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "VideoUserStatisticsSummaryDto": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "employeeId": {"type": "integer", "format": "int32", "nullable": true}, "employeeName": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "totalViewCount": {"type": "integer", "format": "int32"}, "totalCompleteViewCount": {"type": "integer", "format": "int32"}, "totalViewDuration": {"type": "integer", "format": "int32"}, "totalAnswerCount": {"type": "integer", "format": "int32"}, "totalCorrectAnswerCount": {"type": "integer", "format": "int32"}, "totalRewardCount": {"type": "integer", "format": "int32"}, "totalRewardAmount": {"type": "integer", "format": "int64"}, "totalShareCount": {"type": "integer", "format": "int32"}, "totalLikeCount": {"type": "integer", "format": "int32"}, "totalCommentCount": {"type": "integer", "format": "int32"}, "totalDownloadCount": {"type": "integer", "format": "int32"}, "avgCompleteRate": {"type": "number", "format": "double"}, "avgCorrectRate": {"type": "number", "format": "double"}, "avgViewDuration": {"type": "number", "format": "double"}, "totalRewardAmountYuan": {"type": "number", "format": "double", "readOnly": true}, "statisticsDays": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "VideoUserTransferDto": {"required": ["toEmployeeId", "userIds"], "type": "object", "properties": {"userIds": {"type": "array", "items": {"type": "string"}}, "toEmployeeId": {"minLength": 1, "type": "string"}, "reason": {"maxLength": 255, "type": "string", "nullable": true}}, "additionalProperties": false}, "VideoUserTransferResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "userId": {"type": "string", "nullable": true}, "userNickname": {"type": "string", "nullable": true}, "fromEmployeeId": {"type": "string", "nullable": true}, "fromEmployeeName": {"type": "string", "nullable": true}, "toEmployeeId": {"type": "string", "nullable": true}, "toEmployeeName": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}, "operatorId": {"type": "string", "nullable": true}, "operatorName": {"type": "string", "nullable": true}, "transferTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "VideoVideoCompleteUploadResponseDto": {"type": "object", "properties": {"videoId": {"type": "integer", "format": "int32"}, "videoUrl": {"type": "string", "nullable": true}, "coverUrl": {"type": "string", "nullable": true}, "duration": {"type": "integer", "format": "int32"}, "fileSize": {"type": "integer", "format": "int64"}, "videoFormat": {"type": "string", "nullable": true}, "resolution": {"type": "string", "nullable": true}, "success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VideoVideoCreateDto": {"required": ["title", "videoUrl"], "type": "object", "properties": {"title": {"maxLength": 255, "minLength": 1, "type": "string"}, "description": {"type": "string", "nullable": true}, "coverUrl": {"maxLength": 255, "type": "string", "nullable": true}, "videoUrl": {"maxLength": 255, "minLength": 1, "type": "string"}, "duration": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "rewardAmount": {"maximum": 999999.99, "minimum": 0, "type": "number", "format": "double"}, "viewCount": {"type": "integer", "format": "int32"}, "questions": {"type": "array", "items": {"$ref": "#/components/schemas/VideoVideoQuestionDto"}, "nullable": true}, "fileId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VideoVideoQuestionDto": {"required": ["options", "questionText"], "type": "object", "properties": {"questionText": {"maxLength": 500, "minLength": 1, "type": "string"}, "orderNum": {"maximum": 1, "minimum": 0, "type": "integer", "format": "int32"}, "options": {"type": "array", "items": {"$ref": "#/components/schemas/VideoVideoQuestionOptionDto"}}}, "additionalProperties": false}, "VideoVideoQuestionOptionDto": {"required": ["optionText"], "type": "object", "properties": {"optionText": {"maxLength": 500, "minLength": 1, "type": "string"}, "isCorrect": {"type": "boolean"}, "orderNum": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "VideoVideoResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "coverUrl": {"type": "string", "nullable": true}, "videoUrl": {"type": "string", "nullable": true}, "duration": {"type": "integer", "format": "int32"}, "createdBy": {"type": "string", "nullable": true}, "creatorName": {"type": "string", "nullable": true}, "rewardAmount": {"type": "number", "format": "double"}, "viewCount": {"type": "integer", "format": "int32"}, "questions": {"type": "array", "items": {"$ref": "#/components/schemas/VideoVideoQuestionDto"}, "nullable": true}, "status": {"type": "integer", "format": "int32"}, "fileId": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "VideoVideoStatisticsDto": {"type": "object", "properties": {"totalViewCount": {"type": "integer", "format": "int32"}, "completeViewCount": {"type": "integer", "format": "int32"}, "completeRate": {"type": "number", "format": "double"}, "averageViewDuration": {"type": "integer", "format": "int32"}, "todayViewCount": {"type": "integer", "format": "int32"}, "weekViewCount": {"type": "integer", "format": "int32"}, "monthViewCount": {"type": "integer", "format": "int32"}, "uniqueViewerCount": {"type": "integer", "format": "int32"}, "repeatViewCount": {"type": "integer", "format": "int32"}, "shareCount": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "publishedCount": {"type": "integer", "format": "int32"}, "pendingCount": {"type": "integer", "format": "int32"}, "offlineCount": {"type": "integer", "format": "int32"}, "totalDuration": {"type": "integer", "format": "int64"}, "lastViewTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "VideoVideoStatusUpdateDto": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "status": {"maximum": 3, "minimum": 0, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "VideoVideoUpdateDto": {"required": ["id", "title", "videoUrl"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"maxLength": 255, "minLength": 1, "type": "string"}, "description": {"type": "string", "nullable": true}, "coverUrl": {"maxLength": 255, "type": "string", "nullable": true}, "videoUrl": {"maxLength": 255, "minLength": 1, "type": "string"}, "duration": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "rewardAmount": {"maximum": 999999.99, "minimum": 0, "type": "number", "format": "double"}, "viewCount": {"type": "integer", "format": "int32"}, "questions": {"type": "array", "items": {"$ref": "#/components/schemas/VideoVideoQuestionDto"}, "nullable": true}, "fileId": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "VideoVideoUploadResponseDto": {"type": "object", "properties": {"originalVideoUrl": {"type": "string", "nullable": true}, "compressedVideoUrl": {"type": "string", "nullable": true}, "thumbnailUrl": {"type": "string", "nullable": true}, "duration": {"type": "integer", "format": "int32"}, "originalFileSize": {"type": "integer", "format": "int64"}, "compressedFileSize": {"type": "integer", "format": "int64", "nullable": true}, "videoFormat": {"type": "string", "nullable": true}, "resolution": {"type": "string", "nullable": true}, "compressionInProgress": {"type": "boolean"}, "compressionProgress": {"type": "integer", "format": "int32"}, "fileId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VideoWatchProgressUpdateDto": {"required": ["batchId"], "type": "object", "properties": {"batchId": {"type": "integer", "format": "int32"}, "viewDuration": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "watchProgress": {"maximum": 1, "minimum": 0, "type": "number", "format": "double"}, "isCompleted": {"type": "boolean"}}, "additionalProperties": false}, "VideoWatchStatusDto": {"type": "object", "properties": {"hasRecord": {"type": "boolean"}, "hasWatched": {"type": "boolean"}, "isCompleted": {"type": "boolean"}, "watchProgress": {"type": "number", "format": "double"}, "watchProgressPercent": {"type": "number", "format": "double"}, "viewDuration": {"type": "integer", "format": "int32"}, "startTime": {"type": "string", "format": "date-time", "nullable": true}, "lastWatchTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "VideoWechatAccessTokenCreateDto": {"required": ["accessToken", "appId", "expiresAt"], "type": "object", "properties": {"appId": {"maxLength": 100, "minLength": 1, "type": "string"}, "accessToken": {"maxLength": 512, "minLength": 1, "type": "string"}, "expiresAt": {"type": "string", "format": "date-time"}, "tokenType": {"maxLength": 50, "type": "string", "nullable": true}, "isValid": {"type": "integer", "format": "int32"}, "expiresIn": {"type": "integer", "format": "int32", "nullable": true}, "refreshToken": {"maxLength": 512, "type": "string", "nullable": true}, "scope": {"maxLength": 200, "type": "string", "nullable": true}}, "additionalProperties": false}, "VideoWechatAccessTokenResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "appId": {"type": "string", "nullable": true}, "accessToken": {"type": "string", "nullable": true}, "expiresAt": {"type": "string", "format": "date-time"}, "tokenType": {"type": "string", "nullable": true}, "isValid": {"type": "integer", "format": "int32"}, "isExpired": {"type": "boolean", "readOnly": true}, "remainingSeconds": {"type": "integer", "format": "int64", "readOnly": true}, "obtainTime": {"type": "string", "format": "date-time"}, "expiresIn": {"type": "integer", "format": "int32"}, "expiresTime": {"type": "string", "format": "date-time"}, "refreshToken": {"type": "string", "nullable": true}, "scope": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "VideoWechatAccessTokenUpdateDto": {"type": "object", "properties": {"accessToken": {"maxLength": 512, "type": "string", "nullable": true}, "expiresAt": {"type": "string", "format": "date-time", "nullable": true}, "tokenType": {"maxLength": 50, "type": "string", "nullable": true}, "isValid": {"type": "integer", "format": "int32", "nullable": true}, "id": {"type": "integer", "format": "int32"}, "expiresIn": {"type": "integer", "format": "int32", "nullable": true}, "refreshToken": {"maxLength": 512, "type": "string", "nullable": true}, "scope": {"maxLength": 200, "type": "string", "nullable": true}}, "additionalProperties": false}, "VideoWechatLoginDto": {"required": ["code"], "type": "object", "properties": {"code": {"minLength": 1, "type": "string"}, "state": {"type": "string", "nullable": true}, "employeeId": {"maxLength": 50, "type": "string", "nullable": true}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "encryptedData": {"type": "string", "nullable": true}, "iv": {"type": "string", "nullable": true}, "rawData": {"type": "string", "nullable": true}, "signature": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VideoWechatPaymentCallbackDto": {"required": ["outTradeNo", "status", "transactionId"], "type": "object", "properties": {"orderNo": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "resultCode": {"type": "string", "nullable": true}, "payTime": {"type": "string", "format": "date-time", "nullable": true}, "transactionId": {"maxLength": 100, "minLength": 1, "type": "string"}, "outTradeNo": {"maxLength": 100, "minLength": 1, "type": "string"}, "status": {"type": "integer", "format": "int32"}, "wechatResponse": {"type": "string", "nullable": true}, "failReason": {"maxLength": 500, "type": "string", "nullable": true}}, "additionalProperties": false}, "VideoWechatPaymentCreateDto": {"required": ["amount", "openId", "outTradeNo", "rewardId", "userId"], "type": "object", "properties": {"rewardId": {"type": "integer", "format": "int32"}, "userId": {"minLength": 1, "type": "string"}, "openId": {"maxLength": 100, "minLength": 1, "type": "string"}, "outTradeNo": {"maxLength": 100, "minLength": 1, "type": "string"}, "amount": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}, "description": {"maxLength": 200, "type": "string", "nullable": true}, "currency": {"maxLength": 10, "type": "string", "nullable": true}, "subject": {"maxLength": 200, "type": "string", "nullable": true}, "body": {"type": "string", "nullable": true}, "paymentType": {"maxLength": 50, "type": "string", "nullable": true}, "notifyUrl": {"maxLength": 500, "type": "string", "nullable": true}, "returnUrl": {"maxLength": 500, "type": "string", "nullable": true}}, "additionalProperties": false}, "VideoWechatPaymentQueryDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "orderField": {"type": "string", "nullable": true}, "isAsc": {"type": "boolean"}, "rewardId": {"type": "integer", "format": "int32", "nullable": true}, "userId": {"type": "string", "nullable": true}, "openId": {"type": "string", "nullable": true}, "outTradeNo": {"type": "string", "nullable": true}, "transactionId": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "payStartTime": {"type": "string", "format": "date-time", "nullable": true}, "payEndTime": {"type": "string", "format": "date-time", "nullable": true}, "minAmount": {"type": "integer", "format": "int32", "nullable": true}, "maxAmount": {"type": "integer", "format": "int32", "nullable": true}, "orderNo": {"type": "string", "nullable": true}, "paymentType": {"type": "string", "nullable": true}, "startTime": {"type": "string", "format": "date-time", "nullable": true}, "endTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "VideoWechatPaymentResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "rewardId": {"type": "integer", "format": "int32"}, "userId": {"type": "string", "nullable": true}, "userNickname": {"type": "string", "nullable": true}, "openId": {"type": "string", "nullable": true}, "outTradeNo": {"type": "string", "nullable": true}, "orderNo": {"type": "string", "nullable": true}, "currency": {"type": "string", "nullable": true}, "subject": {"type": "string", "nullable": true}, "body": {"type": "string", "nullable": true}, "paymentType": {"type": "string", "nullable": true}, "transactionId": {"type": "string", "nullable": true}, "amount": {"type": "integer", "format": "int32"}, "amountYuan": {"type": "number", "format": "double", "readOnly": true}, "status": {"type": "integer", "format": "int32"}, "statusName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "wechatResponse": {"type": "string", "nullable": true}, "payTime": {"type": "string", "format": "date-time", "nullable": true}, "failReason": {"type": "string", "nullable": true}, "retryCount": {"type": "integer", "format": "int32"}, "refundNo": {"type": "string", "nullable": true}, "refundAmount": {"type": "number", "format": "double"}, "refundReason": {"type": "string", "nullable": true}, "refundTime": {"type": "string", "format": "date-time", "nullable": true}, "notifyUrl": {"type": "string", "nullable": true}, "returnUrl": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "VideoWechatPaymentStatisticsDto": {"type": "object", "properties": {"totalCount": {"type": "integer", "format": "int32"}, "successCount": {"type": "integer", "format": "int32"}, "failCount": {"type": "integer", "format": "int32"}, "failureCount": {"type": "integer", "format": "int32"}, "refundCount": {"type": "integer", "format": "int32"}, "pendingCount": {"type": "integer", "format": "int32"}, "totalAmount": {"type": "integer", "format": "int64"}, "successAmount": {"type": "integer", "format": "int64"}, "totalAmountYuan": {"type": "number", "format": "double", "readOnly": true}, "successAmountYuan": {"type": "number", "format": "double", "readOnly": true}, "successRate": {"type": "number", "format": "double"}, "todayCount": {"type": "integer", "format": "int32"}, "todayAmount": {"type": "integer", "format": "int64"}, "refundAmount": {"type": "number", "format": "double"}, "averageAmount": {"type": "number", "format": "double"}, "todayAmountYuan": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "VideoWechatTokenStatisticsDto": {"type": "object", "properties": {"totalCount": {"type": "integer", "format": "int32"}, "validCount": {"type": "integer", "format": "int32"}, "expiredCount": {"type": "integer", "format": "int32"}, "expiringSoonCount": {"type": "integer", "format": "int32"}, "typeStatistics": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "activeCount": {"type": "integer", "format": "int32"}, "expiringCount": {"type": "integer", "format": "int32"}, "appCount": {"type": "integer", "format": "int32"}, "tokenTypeStats": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "appIdStatistics": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "在下框中输入请求头中需要添加Jwt授权Token：Bearer Token", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}